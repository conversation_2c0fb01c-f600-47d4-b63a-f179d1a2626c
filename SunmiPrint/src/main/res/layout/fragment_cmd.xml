<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="com.sunmi.samples.printerx.ui.vm.CmdViewModel" />
    </data>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="32px"
        android:paddingEnd="32px"
        android:paddingTop="24px">

        <TextView
            android:id="@+id/tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tip_cmd"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/cmd_esc"
            android:layout_width="match_parent"
            android:layout_height="88px"
            android:layout_marginTop="24px"
            android:text="@string/text_cmd_esc"
            android:gravity="center"
            android:onClick="@{()->model.testEsc()}"
            android:background="@drawable/bg_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tip" />


        <TextView
            android:id="@+id/cmd_tspl"
            android:layout_width="match_parent"
            android:layout_height="88px"
            android:layout_marginTop="24px"
            android:text="@string/text_cmd_tspl"
            android:gravity="center"
            android:onClick="@{()->model.testTspl()}"
            android:background="@drawable/bg_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cmd_esc" />

        <TextView
            android:id="@+id/tip_bluetooth"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24px"
            android:text="@string/tip_bluetooth"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cmd_tspl" />

        <TextView
            android:id="@+id/cmd_esc_bluetooth"
            android:layout_width="match_parent"
            android:layout_height="88px"
            android:layout_marginTop="24px"
            android:text="@string/text_bluetooth_esc"
            android:gravity="center"
            android:onClick="@{(view)->model.testBluetoothEsc(view)}"
            android:background="@drawable/bg_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tip_bluetooth" />


        <TextView
            android:id="@+id/cmd_tspl_bluetooth"
            android:layout_width="match_parent"
            android:layout_height="88px"
            android:layout_marginTop="24px"
            android:text="@string/text_bluetooth_tspl"
            android:gravity="center"
            android:onClick="@{(view)->model.testBluetoothTspl(view)}"
            android:background="@drawable/bg_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cmd_esc_bluetooth" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>