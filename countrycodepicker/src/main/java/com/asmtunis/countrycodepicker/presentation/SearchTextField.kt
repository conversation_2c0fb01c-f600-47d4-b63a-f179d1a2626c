package com.asmtunis.countrycodepicker.presentation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.twotone.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.asm.countrycodepicker.R
import kotlinx.coroutines.delay

@Composable
 fun SearchTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    hint: String = stringResource(id = R.string.select_country),
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    // LaunchedEffect prevents endless focus request
    LaunchedEffect(focusRequester) {
        focusRequester.requestFocus()
        delay(100) // Make sure you have delay here
        keyboardController?.show()

    }
    OutlinedTextField(
        modifier = modifier
            .focusRequester(focusRequester).fillMaxWidth().padding(start = 52.dp, end = 16.dp),
        shape = RoundedCornerShape(12.dp),
        value = value,
        label = { Text(text = hint, style = MaterialTheme.typography.bodySmall) },
        onValueChange = { query ->
            onValueChange(query)
        },
        placeholder = {
            Text(text = hint, maxLines = 1)
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.TwoTone.Search,
                contentDescription = null,
                modifier = Modifier.padding(horizontal = 3.dp)
            )
//                if (showLeadingIcon) {
//                    IconButton(onClick = {
//                        onSearchValueChange("")//clear text to hide search view // exp searchVisibility  = articlesViewModel.showSearchView || searchTextState.text.isNotEmpty(),
//                        onShowSearchViewChange()
//                    }) {
//                        Icon(
//                            imageVector = Icons.Default.ArrowBackIosNew,
//                            contentDescription = "Clear Search"
//                        )
//                    }
//                }


        },
        trailingIcon = {
            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                AnimatedVisibility(
                    visible = value.isNotEmpty(),
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "",
                        Modifier.clickable { onValueChange("") }
                    )
                }


            }
            // LottieAnim(R.raw.filter_button, 70.dp, onClick = {onFilterClick()})
        },
        maxLines = 1,
        singleLine = true,
        keyboardActions = KeyboardActions(
            onSearch = {
                // onShowSearchViewChange()
                keyboardController?.hide()
            }
        ),
        keyboardOptions = KeyboardOptions(
            imeAction = ImeAction.Search,
            keyboardType = KeyboardType.Password
        )
    )


//        OutlinedTextField(
//            modifier = modifier.focusRequester(focusRequester)
//                .fillMaxWidth().padding(start = 52.dp, end = 16.dp),
//            shape = RoundedCornerShape(12.dp),
//            value = value,
//            onValueChange = onValueChange,
//            label = { Text(text = hint, style = MaterialTheme.typography.bodySmall) },
//            singleLine = true,
//          //  cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
//            textStyle = LocalTextStyle.current.copy(fontSize = fontSize),
//            keyboardActions = KeyboardActions(
//                onSearch = {
//                  //  onShowSearchViewChange()
//                    keyboardController?.hide()
//                }
//            ),
//            keyboardOptions = KeyboardOptions(
//                imeAction = ImeAction.Search,
//                keyboardType = KeyboardType.Password
//            )
////            decorationBox = { innerTextField ->
////                Row(
////                    modifier,
////                    verticalAlignment = Alignment.CenterVertically
////                ) {
////                    if (leadingIcon != null) leadingIcon()
////                    Box(Modifier.weight(1f)) {
////                        if (value.isEmpty()) Text(
////                            hint
////                          //  style = LocalTextStyle.current.copy(color = textColor, fontSize = fontSize)
////                        )
////                        innerTextField()
////                    }
////                    if (trailingIcon != null) trailingIcon()
////                }
////            }
//        )
}