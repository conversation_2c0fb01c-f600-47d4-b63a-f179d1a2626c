package com.asmtunis.countrycodepicker.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material.icons.twotone.ArrowDropDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getCountryName
import com.asmtunis.countrycodepicker.data.utils.getFlags
import com.asmtunis.countrycodepicker.data.utils.getLibCountries
import com.asmtunis.countrycodepicker.data.utils.searchCountry

class CodePicker {
    @SuppressLint("SuspiciousIndentation")
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun CodePickerDialog(
        modifier: Modifier = Modifier,
        padding: Dp = 15.dp,
        defaultSelectedCountry: CountryData = getLibCountries().first(),
        showCountryCode: Boolean = true,
        pickedCountry: (CountryData) -> Unit,
        dialogAppBarColor: Color = MaterialTheme.colorScheme.primary,
        dialogAppBarTextColor: Color = MaterialTheme.colorScheme.primary,
        enabled: Boolean
    ) {
      val countryList: List<CountryData> = getLibCountries()
        var isPickCountry by remember { mutableStateOf(defaultSelectedCountry) }
        var isOpenDialog by remember { mutableStateOf(false) }
        var searchValue by remember { mutableStateOf("") }
        var isSearch by remember { mutableStateOf(false) }

        val context = LocalContext.current
     //   val countryList = parseCountriesJson(context)?: emptyList()


            Row(
                modifier = Modifier
                    .padding(padding)
                    .clickable {
                        if (enabled)  isOpenDialog = true
                    },

                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    modifier = modifier.width(34.dp),
                    painter = painterResource(id = getFlags(isPickCountry.countryCode)),
                    contentDescription = null
                )
                if (showCountryCode) {
                    Text(
                        text = isPickCountry.phoneCode,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 6.dp)
                    )
                    Icon(imageVector = Icons.TwoTone.ArrowDropDown, contentDescription = null)
                }
            }


        // Select Country Dialog
        if (isOpenDialog) {
            Dialog(
                onDismissRequest = { isOpenDialog = false },
                properties = DialogProperties(usePlatformDefaultWidth = false
                )
            ) {
                Scaffold(
                    topBar = {
                         AppBar(

                            titleVisibilty = isSearch && searchValue.isEmpty(),
                            showNavIcon = true,
                             navIcon = Icons.AutoMirrored.Rounded.ArrowBack,
                            onNavigationClick = {
                                isOpenDialog = false
                                searchValue = ""
                                isSearch = false
                            },
                            //  showNavIcom = clientViewModel.searchTextState.text.isBlank(),
                            actions = {
                                SearchTextField(
                                    value = searchValue,
                                    onValueChange = { searchValue = it },
                                    modifier = Modifier
                                        .clip(RoundedCornerShape(50))
                                        //.height(40.dp),
                                )
                            }
                        )


                    }
                ) { paddingValues ->
                    // it.calculateTopPadding()



                                LazyColumn (
                                    modifier = Modifier.padding(paddingValues)
                                ){
                                    items(
                                        (
                                            if (searchValue.isEmpty()) {
                                                countryList
                                            } else {

                                                countryList.searchCountry(
                                                    key = searchValue,
                                                    countryName = {countryCode -> context.resources.getString(getCountryName(countryCode))}
                                                )
                                            }
                                            )
                                    ) {countryItem ->


                                            Row(
                                                Modifier
                                                    .padding(
                                                        horizontal = 18.dp,
                                                        vertical = 18.dp
                                                    )
                                                    .fillMaxWidth()
                                                    .clickable {
                                                        pickedCountry(countryItem)
                                                        isPickCountry = countryItem
                                                        isOpenDialog = false
                                                        searchValue = ""
                                                        isSearch = false
                                                    },
                                                horizontalArrangement = Arrangement.Start,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Image(
                                                    modifier = modifier.width(30.dp),
                                                    painter = painterResource(
                                                        id = getFlags(
                                                            countryItem.countryCode
                                                        )
                                                    ),
                                                    contentDescription = null
                                                )
                                                Text(
                                                    modifier =  Modifier.padding(horizontal = 18.dp),
                                                  text =  stringResource(id = getCountryName(countryItem.countryCode.lowercase())),
                                                )
                                            }




                                    }
                                }



                }
            }
        }
    }


}
