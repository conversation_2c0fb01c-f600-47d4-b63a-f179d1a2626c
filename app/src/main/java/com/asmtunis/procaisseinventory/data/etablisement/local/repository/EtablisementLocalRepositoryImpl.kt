package com.asmtunis.procaisseinventory.data.etablisement.local.repository

import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.etablisement.local.dao.EtablisementDAO
import kotlinx.coroutines.flow.Flow

class EtablisementLocalRepositoryImpl(
    private val etablisementDAO: EtablisementDAO
) : EtablisementLocalRepository {

    override fun upsertAll(value: Etablisement) = etablisementDAO.insertAll(value)

    override fun deleteAll() = etablisementDAO.deleteAll()

    override fun getAll(): Flow<List<Etablisement>?> = etablisementDAO.all
}
