package com.asmtunis.procaisseinventory.data.sessioncaisse.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.dao.SessionCaisseDAO
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class SessionCaisseModule {

    @Provides
    @Singleton
    fun provideSessionCaisseDao(
        sessionCaisseBase: ProCaisseDataBase
    ) = sessionCaisseBase.sessionCaisseDAO()

    @Provides
    @Singleton
    @Named("SessionCaisse")
    fun provideClientsRepository(
        sessionCaisseDAO: SessionCaisseDAO
    ): SessionCaisseLocalRepository = SessionCaisseLocalRepositoryImpl(sessionCaisseDAO = sessionCaisseDAO)


}