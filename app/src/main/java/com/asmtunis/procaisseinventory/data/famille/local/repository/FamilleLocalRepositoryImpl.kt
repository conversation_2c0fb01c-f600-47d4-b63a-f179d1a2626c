package com.asmtunis.procaisseinventory.data.famille.local.repository

import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.famille.local.dao.FamilleDAO
import kotlinx.coroutines.flow.Flow


class FamilleLocalRepositoryImpl(
        private val familleDAO: FamilleDAO
    ) : FamilleLocalRepository {
    override fun upsertAll(value: List<Famille>) =
        familleDAO.insertAll(value)

    override fun upsert(value: Famille) =
        familleDAO.insert(value)

    override fun deleteAll() =
        familleDAO.deleteAll()

    override fun updateSyncFamille(famCode: String) =
        familleDAO.updateSyncFamille(famCode)

    override fun getAll(): Flow<List<Famille>> =
        familleDAO.all

    override fun getNotSync(): Flow<List<Famille>> =
        familleDAO.nonSync

    override fun getNewCode(prefix: String): Flow<String> =
        familleDAO.getNewCode(prefix)
}