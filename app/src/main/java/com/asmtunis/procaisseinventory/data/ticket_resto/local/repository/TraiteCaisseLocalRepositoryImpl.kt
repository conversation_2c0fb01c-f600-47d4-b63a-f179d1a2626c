package com.asmtunis.procaisseinventory.data.ticket_resto.local.repository

import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.ticket_resto.local.dao.TraiteCaisseDAO
import kotlinx.coroutines.flow.Flow


class TraiteCaisseLocalRepositoryImpl(private val traiteCaisseDAO: TraiteCaisseDAO) :
    TraiteCaisseLocalRepository {
    override fun upsertAll(value: List<TraiteCaisse>) = traiteCaisseDAO.insertAll(value)

    override fun upsert(value: TraiteCaisse) = traiteCaisseDAO.insert(value)
    override fun updateRegCodeAndState(regCode: String, regCodeM: String)
    = traiteCaisseDAO.updateRegCodeAndState(regCode, regCodeM)

    override fun deleteAll() = traiteCaisseDAO.deleteAll()
    override fun deleteByCodeM(codeM: String, exercice: String) =  traiteCaisseDAO.deleteByCodeM(codeM = codeM, exercice = exercice)

    override fun getAll(): Flow<List<TraiteCaisse>>  = traiteCaisseDAO.all

}