package com.asmtunis.procaisseinventory.data.cheque_caisse.remote.di

import com.asmtunis.procaisseinventory.data.cheque_caisse.remote.api.ChequeCaisseApi
import com.asmtunis.procaisseinventory.data.cheque_caisse.remote.api.ChequeCaisseApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object ChequeCaisseRemoteModule {

    @Provides
    @Singleton
    fun provideChequeCaisseApi(client: HttpClient): ChequeCaisseApi = ChequeCaisseApiImpl(client)


}