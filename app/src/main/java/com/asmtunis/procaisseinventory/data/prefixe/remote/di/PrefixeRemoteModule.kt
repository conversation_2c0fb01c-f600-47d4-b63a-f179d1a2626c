package com.asmtunis.procaisseinventory.data.prefixe.remote.di

import com.asmtunis.procaisseinventory.data.prefixe.remote.api.PrefixApi
import com.asmtunis.procaisseinventory.data.prefixe.remote.api.PrefixApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object PrefixeRemoteModule {
        @Provides
        @Singleton
        fun providePrefixeApi(client: HttpClient): PrefixApi = PrefixApiImpl(client)

    }