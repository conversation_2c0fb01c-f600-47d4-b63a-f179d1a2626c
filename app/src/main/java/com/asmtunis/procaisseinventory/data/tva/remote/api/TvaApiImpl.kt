package com.asmtunis.procaisseinventory.data.tva.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TvaApiImpl(private val client: HttpClient) : TvaApi {
    override suspend fun getTVA(baseConfig: String): Flow<DataResult<List<Tva>>> = flow {

        val result = executePostApiCall<List<Tva>>(
            client = client,
            endpoint = Urls.GET_TVA,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }