package com.asmtunis.procaisseinventory.data.devise.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.DEVISE_TABLE
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import kotlinx.coroutines.flow.Flow


@Dao
interface DeviseDAO {
    @get:Query("SELECT * FROM $DEVISE_TABLE")
    val all: Flow<List<Devise>>

    @Query("SELECT * FROM Devise WHERE $DEVISE_TABLE = :devise ")
    fun getOneByDevise(devise: String): Flow<Devise>

    @get:Query("SELECT * FROM $DEVISE_TABLE WHERE Activite = '1' ")
    val activeOne: Flow<Devise?>

    @get:Query("SELECT * FROM $DEVISE_TABLE LIMIT 1")
    val one: Flow<Devise>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Devise)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Devise>)

    @Query("DELETE FROM $DEVISE_TABLE")
    fun deleteAll()
}
