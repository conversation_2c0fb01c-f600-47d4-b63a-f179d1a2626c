package com.asmtunis.procaisseinventory.data.tva.remote.di

import com.asmtunis.procaisseinventory.data.tva.remote.api.TvaApi
import com.asmtunis.procaisseinventory.data.tva.remote.api.TvaApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object TvaRemoteModule {

    @Provides
    @Singleton
    fun provideTvaApi(client: HttpClient): TvaApi = TvaApiImpl(client)

}