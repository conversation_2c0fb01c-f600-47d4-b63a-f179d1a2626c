package com.asmtunis.procaisseinventory.data.station.local.station.repository

import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.local.station.dao.StationDAO
import kotlinx.coroutines.flow.Flow


class StationLocalRepositoryImpl(
    private val stationDAO: StationDAO
) : StationLocalRepository {
    override fun upsertAll(value: List<Station>) = stationDAO.insertAll(value)

    override fun upsert(value: Station) = stationDAO.insert(value)

    override fun deleteAll() = stationDAO.deleteAll()

    override fun deleteByCode(code: String) = stationDAO.deleteByCode(code)

    override fun getByCode(code: String): Flow<Station?> = stationDAO.getByCode(code)

    override fun getAll(): Flow<List<Station>> = stationDAO.all

}