package com.asmtunis.procaisseinventory.data.image_piece_joint.local.repository

import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.coroutines.flow.Flow


interface InventairePieceJointLocalRepository {

    fun updateImageState(codeImage: String)
    fun noSynced(typeVC: String): Flow<List<ImagePieceJoint>>
    fun insertAll(imagePieceJoints: List<ImagePieceJoint>)


    fun insert(imagePieceJoints: ImagePieceJoint)
    fun deleteAll()
    fun deleteByCode(codeMob: String, codeArt: String)

    fun delete(image: ImagePieceJoint)

    fun deleteByCodeImg(codeImage: String)
    fun deleteByDevNumNotSync(devNum: String)

    fun setToInserted(devNum: String)
}