package com.asmtunis.procaisseinventory.data.unite.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import kotlinx.coroutines.flow.Flow


interface UniteApi {
    suspend fun getUnite(baseConfig: String): Flow<DataResult<List<Unite>>>
    suspend fun addUniteMobile(baseConfig: String): Flow<DataResult<List<Unite>>>
}