package com.asmtunis.procaisseinventory.data.banques.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BanqueApiImpl(private val client: HttpClient) : BanqueApi {
    override suspend fun getBanques(baseConfig: String): Flow<DataResult<List<Banque>>> = flow {
        val result = executePostApiCall<List<Banque>>(
            client = client,
            endpoint = Urls.GET_BANQUES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }