package com.asmtunis.procaisseinventory.data.etablisement.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.ETABLISSEMENT_TABLE,primaryKeys = ["Code_Et"])
@Serializable
data class Etablisement (
    @SerialName("Code_Et")
    @ColumnInfo(name = "Code_Et")
    var codeEt: String = "",

    @SerialName("Desg_Et")
    @ColumnInfo(name = "Desg_Et")
    var desgEt: String? = null,

    @SerialName("Et_Adresse")
    @ColumnInfo(name = "Et_Adresse")
    var etAdresse: String? = null,

    @SerialName("Et_Activite")
    @ColumnInfo(name = "Et_Activite")
    var etActivite: String? = null,

    @SerialName("Et_MAtriculeF")
    @ColumnInfo(name = "Et_MAtriculeF")
    var etMAtriculeF: String? = null,

    @SerialName("Et_TVA")
    @ColumnInfo(name = "Et_TVA")
    var etTVA: String? = null,

    @SerialName("Et_Tel1")
    @ColumnInfo(name = "Et_Tel1")
    var etTel1: String? = null,

    @SerialName("Et_Tel2")
    @ColumnInfo(name = "Et_Tel2")
    var etTel2: String? = null,

    @SerialName("Et_Fax")
    @ColumnInfo(name = "Et_Fax")
    var etFax: String? = null
)