package com.asmtunis.procaisseinventory.data.parametrages.local.repository

import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.parametrages.local.dao.ParametrageDAO
import kotlinx.coroutines.flow.Flow


class ParametrageLocalRepositoryImpl(
        private val parametrageDAO: ParametrageDAO
    ) : ParametrageLocalRepository {
    override fun upsert(value: Parametrages) = parametrageDAO.insert(value)

    override fun deleteAll() = parametrageDAO.deleteAll()

    override fun getOne(): Flow<Parametrages?> = parametrageDAO.one
}