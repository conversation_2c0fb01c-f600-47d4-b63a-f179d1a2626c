package com.asmtunis.procaisseinventory.data.station.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.STATION_TABLE
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = STATION_TABLE)
@Serializable
data class Station  (
    @PrimaryKey
    @SerialName("STAT_Code")
    @ColumnInfo(name = "STAT_Code")
    var sTATCode: String = "",

    @SerialName("STAT_Desg")
    @ColumnInfo(name = "STAT_Desg")
    var sTATDesg: String = "",

    @SerialName("STAT_Adresse")
    @ColumnInfo(name = "STAT_Adresse")
    var sTATAdresse: String? = "",

    @SerialName("STAT_Tel1")
    @ColumnInfo(name = "STAT_Tel1")
    var sTATTel1: String? = "",

    @SerialName("STAT_Tel2")
    @ColumnInfo(name = "STAT_Tel2")
    var sTATTel2: String? = "",

    @SerialName("STAT_Fax")
    @ColumnInfo(name = "STAT_Fax")
    var sTATFax: String? = "",

    @SerialName("STAT_Surface")
    @ColumnInfo(name = "STAT_Surface")
    var sTATSurface: String? = "",

    @SerialName("STAT_Emplacement")
    @ColumnInfo(name = "STAT_Emplacement")
    var sTATEmplacement: String? = "",

    @SerialName("STAT_Type")
    @ColumnInfo(name = "STAT_Type")
    var sTATType: String? = "",

    @SerialName("STAT_Etat")
    @ColumnInfo(name = "STAT_Etat")
    var sTATEtat: String? = "",

    @SerialName("STAT_station")
    @ColumnInfo(name = "STAT_station")
    var sTATStation: String? = "",

    @SerialName("STAT_user")
    @ColumnInfo(name = "STAT_user")
    var sTATUser: String? = "",

    @SerialName("STAT_export")
    @ColumnInfo(name = "STAT_export")
    var sTATExport: String? = "",

    @SerialName("STAT_DDm")
    @ColumnInfo(name = "STAT_DDm")
    var sTATDDm: String? = "",

    @SerialName("STAT_SoldeDD")
    @ColumnInfo(name = "STAT_SoldeDD")
    var sTATSoldeDD: String? = "",

    @SerialName("STAT_SoldeDF")
    @ColumnInfo(name = "STAT_SoldeDF")
    var sTATSoldeDF: String? = "",

    @SerialName("STAT_Solde")
    @ColumnInfo(name = "STAT_Solde")
    var sTATSolde: String? = "",

    @SerialName("STAT_Arabe")
    @ColumnInfo(name = "STAT_Arabe")
    var sTATArabe: String? = "",

    @SerialName("Adresse_Arabe")
    @ColumnInfo(name = "Adresse_Arabe")
    var adresseArabe: String? = "",

    @SerialName("MAtriculeF")
    @ColumnInfo(name = "MAtriculeF")
    var mAtriculeF: String? = "",

    @SerialName("TVA")
    @ColumnInfo(name = "TVA")
    var tVA: String? = "",

    @SerialName("Activite")
    @ColumnInfo(name = "Activite")
    var activite: String? = "",

    @SerialName("Stat_Prefixe")
    @ColumnInfo(name = "Stat_Prefixe")
    var statPrefixe: String? = "",

    @SerialName("STAT_Fidelite")
    @ColumnInfo(name = "STAT_Fidelite")
    var sTATFidelite: String? = "",

    @SerialName("STAT_CA")
    @ColumnInfo(name = "STAT_CA")
    var sTATCA: String? = "",

    @SerialName("STAT_BA")
    @ColumnInfo(name = "STAT_BA")
    var sTATBA: String? = "",

    @SerialName("ddm")
    @ColumnInfo(name = "ddm")
    var ddm: String? = "",

    @SerialName("export")
    @ColumnInfo(name = "export")
    var export: String? = "",

    @SerialName("STAT_Et")
    @ColumnInfo(name = "STAT_Et")
    var sTATEt: String? = "",

    @SerialName("Is_Prix_Station")
    @ColumnInfo(name = "Is_Prix_Station")
    var isPrixStation: String? = "",

    @SerialName("STAT_Latitude")
    @ColumnInfo(name = "STAT_Latitude")
    var sTATLatitude: String? = "",

    @SerialName("STAT_Longitude")
    @ColumnInfo(name = "STAT_Longitude")
    var sTATLongitude: String? = "",

    @SerialName("CLI_Code1")
    @ColumnInfo(name = "CLI_Code1")
    var cLICode1: String? = "",

    @SerialName("Code_Ut1")
    @ColumnInfo(name = "Code_Ut1")
    var codeUt1: String? = "",

    @SerialName("Stat_Info1")
    @ColumnInfo(name = "Stat_Info1")
    var statInfo1: String? = "",

    @SerialName("Stat_Info2")
    @ColumnInfo(name = "Stat_Info2")
    var statInfo2: String? = "",

    @SerialName("Stat_Info3")
    @ColumnInfo(name = "Stat_Info3")
    var statInfo3: String? = "",

    @SerialName("Stat_Info4")
    @ColumnInfo(name = "Stat_Info4")
    var statInfo4: String? = "",

    @SerialName("Stat_Info5")
    @ColumnInfo(name = "Stat_Info5")
    var statInfo5: String? = "",

    @SerialName("Type_Prix")
    @ColumnInfo(name = "Type_Prix")
    var typePrix: String? = "",

    @SerialName("Type_PrixSite")
    @ColumnInfo(name = "Type_PrixSite")
    var typePrixSite: String? = "",

    @SerialName("STATMetrageM")
    @ColumnInfo(name = "STATMetrageM")
    var statMetrageM : Double = 0.0,

    @SerialName("STATSecondeM")
    @ColumnInfo(name = "STATSecondeM")
    var statSecondeM : Double = 0.0

   // @Ignore
    //var colorCode = 0

    )



