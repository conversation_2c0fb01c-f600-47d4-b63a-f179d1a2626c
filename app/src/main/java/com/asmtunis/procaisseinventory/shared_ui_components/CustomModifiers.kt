package com.asmtunis.procaisseinventory.shared_ui_components

import android.content.res.Configuration
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.dp

object CustomModifiers {







//    @Composable
//    fun Modifier.customWidth(percentage: Float): Modifier {
//        val configuration = LocalConfiguration.current
//        val screenWidthDp = remember { mutableIntStateOf(configuration.smallestScreenWidthDp) }
//
//        // Update screenWidthDp on configuration changes
//        DisposableEffect(Unit) {
//            onDispose {
//                screenWidthDp.intValue = configuration.screenWidthDp
//            }
//        }
//
//        return requiredWidth(screenWidthDp.intValue.dp * percentage)
//    }


    @Composable
    fun Modifier.customWidth(
       // configuration: Configuration,
        percentage : Float
        ):Modifier {
        val configuration = LocalConfiguration.current
        return requiredWidth(configuration.screenWidthDp.dp * percentage)
    }


    fun Modifier.customHeight(
        configuration: Configuration,
        percentage : Float
    ):Modifier =
        requiredHeight(configuration.screenHeightDp.dp * percentage)





    }
