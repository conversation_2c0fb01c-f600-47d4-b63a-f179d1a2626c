package com.asmtunis.procaisseinventory.shared_ui_components.cameraview

import android.annotation.SuppressLint
import android.net.Uri
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.camera.CameraXScreen
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.gallery.GallerySelect
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.FlashLightComposable
import kotlinx.coroutines.ExperimentalCoroutinesApi


@SuppressLint("SuspiciousIndentation")
@ExperimentalCoroutinesApi
@ExperimentalPermissionsApi
@Composable
fun MainImageTiket(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    cameraViewModel: CameraViewModel,
    barCodeViewModel: BarCodeViewModel
) {
    val context = LocalContext.current
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.gallery))
    val progress by animateLottieCompositionAsState(
        composition = composition,
        restartOnPlay = false,
      //  iterations = Int.MAX_VALUE
    )


        if (cameraViewModel.showGallerySelect) {
            GallerySelect(
                onImageUri = { uri ->
                    cameraViewModel.onshowGallerySelect(false)
                    popBackStack()

                    cameraViewModel.addImageUri(imgUri = uri, context = context)
                }
            )
        } else {
            Box(
                modifier = Modifier.padding(30.dp)
            ) {
                CameraXScreen(
                    navigate = { navigate(it) },
                    popBackStack = { popBackStack() },
                    onImageFile = { file ->
                       cameraViewModel.addImageUri(imgUri = file.toUri(), context = context)
                    },
                    barCodeViewModel=  barCodeViewModel
                )
                FlashLightComposable(
                    flashOn = barCodeViewModel.flashState,
                onflashStateChange = {
                    barCodeViewModel.onflashState(it)
                }
                )
                LottieAnimation(
                    modifier=Modifier.size(70.dp)
                        .padding(16.dp)
                        .align(Alignment.BottomEnd)
                        .clickable { cameraViewModel.onshowGallerySelect(true) },
                    composition = composition,
                    progress = { progress }
                )


            }
        }
  //  }
}

val EMPTY_IMAGE_URI: Uri = Uri.parse("file://dev/null")


