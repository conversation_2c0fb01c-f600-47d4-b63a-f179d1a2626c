package com.asmtunis.procaisseinventory.shared_ui_components.cameraview

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.ImageUtils
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import javax.inject.Inject

class CameraViewModel
    @Inject
    constructor() : ViewModel() {
        var imageUriList = mutableStateListOf<Uri>()
            private set

        var currentImageUri: Uri? by mutableStateOf(null)
            private set

        var numSerie: String by mutableStateOf("")
            private set

        fun onNumChange(value: String) {
            numSerie = value
        }

        fun deleteImageTaken(imgUri: Uri) {
            imageUriList.remove(imgUri)
            listImgeUri.removeIf { it.imgUrl == imgUri.toString() }
        }

    fun clearListImgeUri() {
        listImgeUri.clear()
    }

        fun addImageUri(
            imgUri: Uri,
            context: Context,
        ) {
            if (imgUri == EMPTY_IMAGE_URI) {
                imageUriList.clear()
                currentImageUri = null
            } else {
                currentImageUri = imgUri
                imageUriList.add(imgUri)

                addImageUri(numSerie = numSerie, context = context)
            }
        }

        var showGallerySelect by mutableStateOf(false)
            private set

        fun onshowGallerySelect(showGallerySlct: Boolean) {
            showGallerySelect = showGallerySlct
        }

        var listImgeUri = mutableStateListOf<ImagePieceJoint>()
            private set

        var currentImagePageDialog: Int by mutableIntStateOf(0)
            private set

        fun onCurrentImagePageDialogChange(value: Int) {
            currentImagePageDialog = value
        }

        var openVerticalalImagePagerDialog: Boolean by mutableStateOf(false)
            private set

        fun onOpenVerticalalImagePagerDialogChange(value: Boolean) {
            openVerticalalImagePagerDialog = value
        }



        private fun addImageUri(
            numSerie: String,
            context: Context,
        ) {
                val imagePieceJoint =
                    ImagePieceJoint(
                        imgUrl = currentImageUri.toString(),
                        devNum = numSerie,
                        image = if (currentImageUri == null) null else ImageUtils.uriToBase64(context, currentImageUri!!),
                        vcNumSerie = numSerie,
                    )


                imagePieceJoint.status = ItemStatus.INSERTED.status
                imagePieceJoint.isSync = false

                listImgeUri.add(imagePieceJoint)


        }

        fun addListImageUri(imageList: List<ImagePieceJoint>) {
            listImgeUri.clear()
            listImgeUri.addAll(imageList)
        }
    }
