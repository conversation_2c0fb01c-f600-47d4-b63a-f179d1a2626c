package com.asmtunis.procaisseinventory.shared_ui_components.tables

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages

object TableTextUtils {
    fun infoText(selectedArticle: SelectedArticle, isAchat: Boolean = false): String {
        val priceString = convertStringToPriceFormat(selectedArticle.prixVente)
        val discountAmount = convertStringToDoubleFormat(selectedArticle.mntDiscount)


        val discountString = if (stringToDouble(discountAmount) != 0.0) {

            val discountPercentage = selectedArticle.discount
            "Remise: -${convertStringToPriceFormat(discountAmount)} (-${removeTrailingZeroInDouble(discountPercentage)}%)"
        } else {
            ""
        }

        val tvaString = if (stringToDouble(selectedArticle.tva.tVACode) != 0.0) {
            val tvaRate = convertStringToDoubleFormat(selectedArticle.tva.tVACode)
            val tvaAmount = convertStringToPriceFormat(selectedArticle.mntTva)
            "Tva: $tvaRate% ($tvaAmount)"
        } else {
            ""
        }

      return if(isAchat)
          "Prix Achat HT: ${convertStringToPriceFormat(selectedArticle.lTMtBrutHT)} $discountString $tvaString"
      else {
          if(stringToDouble(selectedArticle.prixVente) == stringToDouble(selectedArticle.prixCaisse))
//              ""
              "$discountString $tvaString" // if modify, test recap bl
          else  "Prix Vente: $priceString $discountString $tvaString"
      }

    }



    fun firstColumn(
        selectedArticle: SelectedArticle,
        parametrage: Parametrages,
        articleMapByBarCode: Map<String, Article>
    ): String {
        val isProMode = parametrage.proModeM
        val article = articleMapByBarCode[selectedArticle.article.aRTCode]?: Article()

        var infoArt = ""
        if(isProMode == 1 && article.cOUDesignation!= null)
            infoArt = " ("+article.cOUDesignation+" "+article.taiTaille+")"
    return (article.aRTDesignation)+ "$infoArt ("+selectedArticle.article.aRTCode+")"
    }
}