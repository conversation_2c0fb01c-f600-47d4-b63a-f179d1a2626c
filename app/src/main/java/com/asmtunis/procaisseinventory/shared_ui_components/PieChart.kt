package com.asmtunis.procaisseinventory.shared_ui_components


import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.data.PieChartSliceData
import com.simapps.ui_kit.ProportionalText
import kotlinx.coroutines.delay
import kotlin.math.abs

data class SliceMetaData(
    val startAngle: Float,
    val sweepAngle: Float,
    val color: Color,
    val radius: Dp,
)

private fun buildPieChartSlices(sliceData: List<PieChartSliceData>): List<SliceMetaData> {
    var lastValue = 0f
    val totalSum = sliceData.sumOf { abs(it.amount) } // use abs to deal with negatif amount like credit
    val pieChartSlices = mutableListOf<SliceMetaData>()
    sliceData.forEach { value ->
        val amount = abs(value.amount) // use abs to deal with negatif amount like credit
        val sweepAngle = if(amount == 0.0) 0.0 else 360 * amount / totalSum.toFloat()
        val slice = SliceMetaData(
            startAngle = lastValue,
            sweepAngle = sweepAngle.toFloat(),
            color = value.color,
            radius = value.radius
        )
        pieChartSlices.add(slice)
        lastValue += sweepAngle.toFloat()
    }
    return pieChartSlices.toList()
}

@Composable
fun PieChart(
    circleLabel: String,
    regTotalMnt: Double,
    priceFormatTotalSum: Boolean,
    sliceData: List<PieChartSliceData>,
    modifier: Modifier = Modifier,
    chartRadius: Dp = 150.dp,
) {

   // val totalSum = sliceData.filter { it.name != UiText.StringResource(resId = R.string.credit_label) }.sumOf { abs(it.amount) }// use abs to deal with negatif amount like credit

    val chartSlices = buildPieChartSlices(sliceData)
    // Pie Chart
    Box(modifier = modifier) {
        chartSlices.forEachIndexed { index, slice ->
            var animate by rememberSaveable { mutableStateOf(false) }
            val animatedSweepAngle by animateFloatAsState(targetValue = if (animate) slice.sweepAngle else 0f, label = "", animationSpec = tween(600))
            LaunchedEffect(key1 = Unit) {
                delay(300L * index)
                animate = true
            }
            Canvas(
                modifier = Modifier
                    .size(chartRadius * 2f)
                    .rotate(-90f)
            ) {
                drawArc(
                    color = slice.color,
                    startAngle = slice.startAngle,
                    sweepAngle = animatedSweepAngle,
                    useCenter = false,
                    style = Stroke(width = slice.radius.toPx(), cap = StrokeCap.Butt)
                )
            }
        }

        Column(modifier = Modifier.align(Alignment.Center), horizontalAlignment = Alignment.CenterHorizontally) {
            Text(text = circleLabel, color = MaterialTheme.colorScheme.error)
            ProportionalText(text = if(priceFormatTotalSum) convertStringToPriceFormat(regTotalMnt.toString()) else removeTrailingZeroInDouble(regTotalMnt.toString()).ifEmpty { "0" })

        }
    }

}


