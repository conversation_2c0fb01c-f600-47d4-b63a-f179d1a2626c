package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.twotone.AddCircle
import androidx.compose.material.icons.twotone.AutoMode
import androidx.compose.material.icons.twotone.DataSaverOn
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.dokar.sonner.ToasterState

@Composable
fun AddViewBottomAppBar(
    haveCameraDevice: Boolean,
    toaster: ToasterState,
    onClickAddArticle: () -> Unit,
    isAutoScanMode: Boolean,
    setAutoScanMode: () -> Unit,
    openBareCodeScanner: () -> Unit,
    onSaveClick: () -> Unit,
    showAddArticleBtn: Boolean = true,
    showAutoScanModeBtn: Boolean = true,
    showBareCodeScannerBtn: Boolean,//= true,
    /**
     * iF NEED TO UPDATE ALL ARTICLE PRICES
     * BUT IF IMPLEMNETED MUST CORRECT CRASH *** TO TEST IF IMPLEMENTED
     */
   // showPriceCategori: Boolean = false,
    showSaveBtn: Boolean = false,
    onPriceCategoryClick: () -> Unit = { }

) {
    BottomAppBar(
        actions = {

            if(showAutoScanModeBtn) {
                IconButton(
                    onClick = {
                        setAutoScanMode()
                    }) {
                    Column(
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Icon(
                            if (isAutoScanMode)  Icons.TwoTone.AutoMode else Icons.TwoTone.DataSaverOn,
                            contentDescription = stringResource(id = R.string.cd_add_client_button)
                        )

                        Text(text = if (isAutoScanMode) stringResource(id = R.string.auto) else stringResource(id = R.string.man), style = MaterialTheme.typography.labelSmall)
                    }


                }
            }

            Spacer(modifier = Modifier.width(12.dp))


            if (showAddArticleBtn) {
                IconButton(
                    onClick = {
                        onClickAddArticle()
                    }) {
                    Column(
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Icon(
                            Icons.TwoTone.AddCircle,
                            contentDescription = stringResource(id = R.string.cd_add_client_button)
                        )

                        Text(text = stringResource(id = R.string.ajout), style = MaterialTheme.typography.labelSmall)
                    }


                }
            }
            /**
             * iF NEED TO UPDATE ALL ARTICLE PRICES
             * BUT IF IMPLEMNETED MUST CORRECT CRASH *** TO TEST IF IMPLEMENTED
             */

//            if (showPriceCategori) {
//                IconButton(
//                    onClick = {
//                        onPriceCategoryClick()
//                    }) {
//                    Column(
//                        verticalArrangement = Arrangement.Top,
//                        horizontalAlignment = Alignment.CenterHorizontally,
//                    ) {
//                        Icon(
//                            Icons.TwoTone.PriceChange,
//                            contentDescription = stringResource(id = R.string.cd_add_client_button)
//                        )
//
//                        Text(text = stringResource(id = R.string.prix), style = MaterialTheme.typography.labelSmall)
//                    }
//
//
//                }
//            }

            Spacer(modifier = Modifier.width(12.dp))
            if(showBareCodeScannerBtn) {
                Column(
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    BareCodeScannerIcon(
                        haveCameraDevice = haveCameraDevice,
                        toaster = toaster,
                        onClick = {
                            openBareCodeScanner()
                        }
                    )

                    Text(text = stringResource(id = R.string.scan), style = MaterialTheme.typography.labelSmall)
                }
            }

        },
        /*
          val density = LocalDensity.current

            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically {
                    with(density) { 40.dp.roundToPx() }
                } + fadeIn(),
                exit = fadeOut(
                    animationSpec = keyframes {
                        this.durationMillis = 120
                    }
                )
            ) {

            }
         */
        floatingActionButton = {

            AnimatedVisibility(
                visible = showSaveBtn,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                var lastClickTime by remember { mutableLongStateOf(0L) }
                FloatingActionButton(
                    onClick = {

                      //  onSaveClick()

                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastClickTime >= 3000) {
                            lastClickTime = currentTime
                            onSaveClick()
                        }
                    },
                    containerColor = BottomAppBarDefaults.bottomAppBarFabColor,
                    elevation = FloatingActionButtonDefaults.bottomAppBarFabElevation()
                ) {
                    Icon(Icons.Filled.Save, "Localized description")
                }
            }

        }
    )
}