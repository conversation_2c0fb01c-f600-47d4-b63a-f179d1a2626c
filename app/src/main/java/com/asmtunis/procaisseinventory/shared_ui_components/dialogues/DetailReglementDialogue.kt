package com.asmtunis.procaisseinventory.shared_ui_components.dialogues

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.AddCard
import androidx.compose.material.icons.twotone.CommentBank
import androidx.compose.material.icons.twotone.CreditCard
import androidx.compose.material.icons.twotone.Info
import androidx.compose.material.icons.twotone.MonetizationOn
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils.getReglementType
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.simapps.ui_kit.custom_cards.ItemDetail

@Composable
fun DetailReglementDialogue(
    reglementCaisse: ReglementCaisse,
    onDismiss: () -> Unit,
    listTicket: List<Ticket>
) {
    val context = LocalContext.current

    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismiss()

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp)
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(top = 16.dp, bottom = 16.dp)
                        .verticalScroll(
                            rememberScrollState()
                        )

                ) {

                    /*ItemDetail(
                        title = stringResource(id = R.string.client_field_title),
                        dataText = reglementCaisse.rEGCNomPrenom?: reglementCaisse.rEGCCodeClient!!,
                        icon = Icons.TwoTone.Info
                    )*/
                    Text(
                        text = reglementCaisse.rEGCNomPrenom?: reglementCaisse.rEGCCodeClient!!,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    ItemDetail(
                        title = stringResource(id = R.string.type_reglement),
                        dataText = getReglementType(
                            reglementCaisse = reglementCaisse,
                            listTicket = listTicket,
                            context = context
                        ),
                        icon = Icons.TwoTone.Info
                    )



                    if (reglementCaisse.rEGCMntEspece!! > 0) {
                        Spacer(modifier = Modifier.height(16.dp))
                        ItemDetail(
                            title = stringResource(id = R.string.espece),
                            dataText = StringUtils.convertStringToPriceFormat(reglementCaisse.rEGCMntEspece.toString()),
                            icon = Icons.TwoTone.MonetizationOn
                        )
                    }

                    if (reglementCaisse.rEGCMntCarteBancaire!! > 0) {
                        Spacer(modifier = Modifier.height(9.dp))
                        ItemDetail(
                            title = stringResource(id = R.string.carte_banquaire),
                            dataText = StringUtils.convertStringToPriceFormat(reglementCaisse.rEGCMntCarteBancaire.toString()),
                            icon = Icons.TwoTone.CreditCard
                        )
                    }

                    if (reglementCaisse.rEGCMntTraite!! > 0) {
                        Spacer(modifier = Modifier.height(9.dp))
                        ItemDetail(
                            title = stringResource(id = R.string.traite),
                            dataText = StringUtils.convertStringToPriceFormat(reglementCaisse.rEGCMntTraite.toString()),
                            icon = Icons.TwoTone.AddCard
                        )
                    }

                    if (reglementCaisse.rEGCMntCheque!! > 0) {
                        Spacer(modifier = Modifier.height(9.dp))
                        ItemDetail(
                            title = stringResource(id = R.string.checK),
                            dataText = StringUtils.convertStringToPriceFormat(reglementCaisse.rEGCMntCheque.toString()),
                            icon = Icons.TwoTone.CommentBank
                        )
                    }
                    Spacer(modifier = Modifier.padding(top = 16.dp))
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 30.dp, end = 30.dp),
                        color = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.padding(top = 12.dp))
                    Text(
                        text = stringResource(id = R.string.total_, StringUtils.convertStringToPriceFormat(reglementCaisse.rEGCMontant.toString())),
                        textAlign = TextAlign.Center,

                        )
                    Spacer(modifier = Modifier.padding(top = 12.dp))
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 30.dp, end = 30.dp),
                        color = MaterialTheme.colorScheme.error
                    )

                    Spacer(modifier = Modifier.padding(top = 12.dp))

                    OutlinedButton(
                        onClick = onDismiss
                    ) {
                        Text(text = stringResource(id = R.string.quitter))
                    }


                }
            }

        }
    )
}