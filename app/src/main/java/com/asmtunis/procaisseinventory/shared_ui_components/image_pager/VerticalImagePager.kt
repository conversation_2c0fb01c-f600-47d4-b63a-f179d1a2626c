package com.asmtunis.procaisseinventory.shared_ui_components.image_pager

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.simapps.ui_kit.ModifiersUtils.graphicsLayer

@Composable
fun VerticalImagePager(
    onDismissRequest: () -> Unit,
    imageList: List<ImagePieceJoint>,
    canModify: Boolean,
    onDeleteClick: (ImagePieceJoint) -> Unit,
) {

    Dialog(
        onDismissRequest = {
            onDismissRequest()
        },
        properties =
            DialogProperties(
                usePlatformDefaultWidth = false,
            ),
    ) {
        val pagerState =
            rememberPagerState(
                initialPage = 0,
                initialPageOffsetFraction = 0f,
            ) {
                // provide pageCount
                imageList.size
            }
        LaunchedEffect(
            key1 = imageList.size,
        ) {
           // if (imageList.isEmpty()) onDismissRequest()
        }

        // Calculate the absolute offset for the current page from the
        // scroll position. We use the absolute value which allows us to mirror
        // any effects for both directions
        // val pageOffset = calculateCurrentOffsetForPage(page).absoluteValue
        // We animate the scaleX + scaleY, between 85% and 100%
        VerticalPager(
            modifier = Modifier.fillMaxHeight(),
            state = pagerState,
            pageSpacing = 8.dp,
            contentPadding = PaddingValues(vertical = 32.dp),
            pageContent = { page ->
                Card(
                    Modifier.graphicsLayer(pagerState = pagerState, page = page),
                ) {
                    PageContent(
                        page = page,
                        canModify = canModify,
                        imageList = imageList,
                        onDismissRequest = { onDismissRequest() },
                        onDeleteClick = { onDeleteClick(it) }
                    )
                }
            },
        )
    }
}
