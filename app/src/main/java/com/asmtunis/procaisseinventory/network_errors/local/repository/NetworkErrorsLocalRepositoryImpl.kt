package com.asmtunis.procaisseinventory.network_errors.local.repository

import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.network_errors.local.dao.NetworkErrorsDAO
import kotlinx.coroutines.flow.Flow


class NetworkErrorsLocalRepositoryImpl(
        private val networkErrorsDAO: NetworkErrorsDAO
    ) : NetworkErrorsLocalRepository {
    override fun getAll(): Flow<List<NetworkError>> = networkErrorsDAO.all

    override fun getOneByUrlAndExtraInfo(url: String, extraInfo: String): Flow<NetworkError> =
        networkErrorsDAO.getOneByUrlAndExtraInfo(url = url, extraInfo = extraInfo)

    override fun insert(item: NetworkError) =
        networkErrorsDAO.insert(item = item)

    override fun deleteByUrlAndExtraInfo(url: String, extraInfo: String) =
        networkErrorsDAO.deleteByUrlAndExtraInfo(url = url, extraInfo = extraInfo)

    override fun deleteAll() = networkErrorsDAO.deleteAll()

}