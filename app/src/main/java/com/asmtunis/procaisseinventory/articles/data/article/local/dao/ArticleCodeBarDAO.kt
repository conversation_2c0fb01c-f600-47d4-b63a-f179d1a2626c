package com.asmtunis.procaisseinventory.articles.data.article.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.Articles_CODE_BAR_TABLE
import kotlinx.coroutines.flow.Flow


@Dao
interface ArticleCodeBarDAO {
    @get:Query("SELECT * FROM $Articles_CODE_BAR_TABLE")
    val all: Flow<List<ArticleCodeBar>>

    @Query("SELECT * FROM $Articles_CODE_BAR_TABLE WHERE isSync=0 and  (Status=:status) ")
    fun getByStatus(status: String):  Flow<List<ArticleCodeBar>>

    @Query("SELECT * FROM $Articles_CODE_BAR_TABLE WHERE (Status=:status) ")
    fun getByStatusForced(status: String): Flow<List<ArticleCodeBar>>

    @Query("SELECT * FROM $Articles_CODE_BAR_TABLE where Fils_CodeBar like  '%' || :code || '%'  or Parent_CodeBar like '%' || :code || '%'")
    fun getParent(code: String): Flow<ArticleCodeBar>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<ArticleCodeBar>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: ArticleCodeBar)

    @Query("UPDATE $Articles_CODE_BAR_TABLE SET isSync=1 , Status='SELECTED'")
    fun updateStatus()

    @Query("DELETE FROM $Articles_CODE_BAR_TABLE")
    fun deleteAll()
}
