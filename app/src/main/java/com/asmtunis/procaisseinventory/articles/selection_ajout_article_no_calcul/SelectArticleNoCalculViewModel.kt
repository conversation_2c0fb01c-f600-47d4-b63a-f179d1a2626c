package com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject

@HiltViewModel
    class SelectArticleNoCalculViewModel @Inject constructor(
        @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
        @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
        @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        private val proCaisseRemote: ProCaisseRemote
    ) : ViewModel() {


        /**
         * Inventory select aticle
         */
        var selectedArticleInventory by mutableStateOf(SelectedArticle())
            private set

        fun setSelectedArticlInventory(value: SelectedArticle, from: String = "v") {
            selectedArticleInventory = value
        }






    var controlQuantity by mutableStateOf(true)
            private set
        fun setControlQte(value: Boolean) {
            controlQuantity = value
        }


        var selectedArticleInventoryList = mutableStateListOf<SelectedArticle>()
            private set

        fun addItemToSelectedArticleInventoryListd(selectedArticleInventory: SelectedArticle) {

            if (selectedArticleInventory == SelectedArticle() || selectedArticleInventory.quantity.isEmpty()) {
                return
            }
            val replaceCurrentArt = selectedArticleInventoryList.any { it.article.aRTCode == selectedArticleInventory.article.aRTCode }

            if (replaceCurrentArt) {
                selectedArticleInventoryList.replaceAll { if (it.article.aRTCode == selectedArticleInventory.article.aRTCode) selectedArticleInventory else it }
            } else {
                selectedArticleInventoryList.add(selectedArticleInventory)
            }
        }


    fun addItemToSelectedArticleInventoryList(articleToAdd: SelectedArticle) {
        if (articleToAdd == SelectedArticle() || articleToAdd.quantity.isBlank()) {
            return
        }

        val isArticlePresent = selectedArticleInventoryList.find { it.article.aRTCode == articleToAdd.article.aRTCode } != null

        if (isArticlePresent) {
            selectedArticleInventoryList.replaceAll {
                if (it.article.aRTCode == articleToAdd.article.aRTCode) articleToAdd else it
            }
        } else {
            selectedArticleInventoryList.add(articleToAdd)
        }
    }

        fun resetSelectedInventoryArticles() {
            selectedArticleInventoryList.clear()
            selectedArticleInventory = SelectedArticle()
        }

        fun setConsultationSelectedArticleInventoryList(
            article: Article,
            numSerie: String = "",
            tva: Tva? = Tva(),
            mntTva:  String = "",
            quantity: String,
            qteStationFromDB: String = "",
            prixCaisse: String = "",
            prixAchatHt: String = "",
            discount: String = "",
            mntDiscount: String = "",
            lTMtTTC: String = "",
            lTMtHT: String = ""
        ) {


            selectedArticleInventoryList.add(
                SelectedArticle(
                    article = article,
                    numSerie = numSerie,
                    tva = tva ?: Tva(),
                    mntTva = mntTva,
                    quantity = quantity,
                    qteStationFromDB = qteStationFromDB,
                    prixCaisse = prixCaisse,
                    discount = discount,
                    mntDiscount = mntDiscount,
                    lTMtTTC = lTMtTTC,
                    lTMtBrutHT = lTMtHT,
                    prixAchatHt = prixAchatHt
                )
            )

        }


    fun setSelectedList(
        listLigneInventaire: List<LigneInventaire>,
        articleMapByBarCode: Map<String, Article>,
        stationStockArticl: Map<String, StationStockArticle>,
    ) {

        for (ligne in listLigneInventaire) {

             val article = articleMapByBarCode[ligne.lGINVCodeArticle]?: Article(aRTCodeBar = ligne.lGINVCodeArticle, aRTCode = ligne.lGINVCodeArticle)

            setConsultationSelectedArticleInventoryList(
                article = article,
                quantity = removeTrailingZeroInDouble(ligne.lGINVQteReel?: "").ifEmpty { "0" },
             //  qteStationFromDB = ligne.lGINVQteStock ?: "N/A",
                  qteStationFromDB = stationStockArticl[ligne.lGINVCodeArticle+ ligne.lGINVStation]?.sARTQteStation?: "",
                 prixCaisse = ligne.lGINVPrixCMP ?: "N/A",
                prixAchatHt = ligne.lGINVPrixCMP ?: "N/A"
            )
        }
    }


        fun deleteItemToSelectedArticleInventoryList(article: Article) {
            selectedArticleInventoryList.removeIf { it.article.aRTCode == article.aRTCode }

        }








    }