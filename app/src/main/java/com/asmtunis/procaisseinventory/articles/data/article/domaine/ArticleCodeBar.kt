package com.asmtunis.procaisseinventory.articles.data.article.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable



@Entity(tableName = ProCaisseConstants.Articles_CODE_BAR_TABLE,primaryKeys = ["Parent_CodeBar","Fils_CodeBar"])
@Serializable
data class ArticleCodeBar(

    @ColumnInfo(name = "Parent_CodeBar")
    @SerialName("Parent_CodeBar")
    var parentCodeBar: String = "",

    @ColumnInfo(name = "Fils_CodeBar")
    @SerialName("Fils_CodeBar")
    var filsCodeBar: String = "",

    @ColumnInfo(name = "cod_b_user")
    @SerialName("cod_b_user")
    var codBUser: String? = "",

    @ColumnInfo(name = "cod_b_station")
    @SerialName("cod_b_station")
    var codBStation: String? = "",

    @ColumnInfo(name = "cod_b_export")
    @SerialName("cod_b_export")
    var codBExport: String? = "",

    @ColumnInfo(name = "cod_b_DDm")
    @SerialName("cod_b_DDm")
    var codBDDm: String? = "",

    @ColumnInfo(name = "cod_Qte")
    @SerialName("cod_Qte")
    var codQte: String? = ""

    ): BaseModel()
