package com.asmtunis.procaisseinventory.articles.data.article.remote.di

import com.asmtunis.procaisseinventory.articles.data.article.remote.api.ArticlesApi
import com.asmtunis.procaisseinventory.articles.data.article.remote.api.ArticlesApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object ArticleRemoteModule {


        @Provides
        @Singleton
        fun provideArticlesApi(client: HttpClient): ArticlesApi = ArticlesApiImpl(client)


    }