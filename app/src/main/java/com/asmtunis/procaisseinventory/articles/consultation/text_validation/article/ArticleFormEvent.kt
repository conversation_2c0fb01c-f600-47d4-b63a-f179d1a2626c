package com.asmtunis.procaisseinventory.articles.consultation.text_validation.article

import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT


sealed class ArticleFormEvent {

    data class DesignationChanged(val designation: String) : ArticleFormEvent()
    data class BarCodeChanged(val barCode: String) : ArticleFormEvent()
    data class RefChanged(val ref: String) : ArticleFormEvent()
    data class PrixHTChanged(val prixHT: String) : ArticleFormEvent()
    data class TypeProduitChanged(val typeProduit: String) : ArticleFormEvent()
    data class PrixTTCChanged(val prixTTC: String) : ArticleFormEvent()
    data class QteTTStationsChanged(val qteTTStations: String) : ArticleFormEvent()
    data class QteStationChanged(val qteStation: String) : ArticleFormEvent()
    data class QteStockChanged(val qteStock: String) : ArticleFormEvent()
    data class FamilleChanged(val famille: Famille) : ArticleFormEvent()
    data class MarqueChanged(val marque: Marque) : ArticleFormEvent()
    data class TypePrixChanged(val typePrix: TypePrixUnitaireHT) : ArticleFormEvent()
    data class TvaChanged(val tva: Tva) : ArticleFormEvent()
    data class UniteChanged(val unite: Unite) : ArticleFormEvent()

    object SubmitAddArticle : ArticleFormEvent()
    object SubmitAddPatrimoine : ArticleFormEvent()

}