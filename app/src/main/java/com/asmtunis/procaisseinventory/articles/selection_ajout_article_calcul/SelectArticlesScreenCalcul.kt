package com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AddCircleOutline
import androidx.compose.material.icons.filled.DeleteOutline
import androidx.compose.material.icons.filled.RemoveCircleOutline
import androidx.compose.material.icons.twotone.ShoppingCart
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getQuantity
import com.asmtunis.procaisseinventory.articles.consultation.screens.StockFilterSegmentedButton
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.StockArticle
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.ui.RadioButtonChoiceView
import com.asmtunis.procaisseinventory.pro_caisse.ui.SetArticleDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.DataBasePaginationLoadState
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.text_animation.AnimatedText
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.roundedCornerShape


@ExperimentalMaterial3Api
@Composable
fun SelectArticlesScreenMobility (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    articlesViewModel: ArticlesViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    selectArtCalculVM: SelectArticleCalculViewModel
    //  searchViewModel: SearchViewModel
) {
    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val isChahiaClient = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.IS_CHAHIA_CLIENT }
    val haveChoosePriceCategoriAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CHOOSE_PRICE_CATEGERI }

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val hasPromo = mainViewModel.hasPromo(mainViewModel.stationList.firstOrNull{it.sTATCode == mainViewModel.utilisateur.Station})


    val priceCategoryList = selectArtCalculVM.priceCategoryList

    val listUnite = mainViewModel.uniteList
    val tvaList = mainViewModel.tvaList

    val isAutoScanMode = mainViewModel.isAutoScanMode
    val selectedArticle = selectArtCalculVM.selectedArticle

   val controlQuantity = selectArtCalculVM.controlQuantity

    val context = LocalContext.current

    val articlesListState = articlesViewModel.articlesListState
    val listOrder = articlesListState.listOrder

    val isProInventory = navigationDrawerViewModel.isProInventory
    val listFilter = articlesListState.filter
    val filterList = context.resources.getStringArray(R.array.article_filter)

    val qty = stringToDouble(selectedArticle.quantity)
    val selectedPriceCategory = selectArtCalculVM.selectedPriceCategory
     val articleMapByBarCode = mainViewModel.articleMapByBarCode
     val articleCodeBarList = mainViewModel.articleCodeBarList
   val showTva = selectArtCalculVM.showTva

    val searchTextState = articlesViewModel.searchTextState
    val articleList: LazyPagingItems<Article>? = articlesListState.lists?.collectAsLazyPagingItems()

    val stockArticleStock = articlesViewModel.stockArticleStock
    val barCodeInfo = barCodeViewModel.barCodeInfo
    val selectedArticleList = selectArtCalculVM.selectedArticleList
    LaunchedEffect(key1 = Unit
//        key1 = searchTextState.text,
//        key2 = articlesListState.lists,
//        key3 = articlesListState.filter
    ) {
        articlesViewModel.filterArticles(articlesListState = articlesListState)
    }
    LaunchedEffect(key1 = stockArticleStock) {
        if (stockArticleStock != StockArticle.SELECTED.filter)
            articlesViewModel.filterArticles(articlesListState = articlesListState)
    }




    LaunchedEffect(key1 = barCodeInfo) {
        if(barCodeInfo.value.isEmpty()) return@LaunchedEffect
        articlesViewModel.onEvent(event = ListEvent.ListSearch(ListSearch.SecondSearch()))
        articlesViewModel.onSearchValueChange(TextFieldValue(barCodeInfo.value))

        selectArtCalculVM.handleBareCodeResult(
            errorMessage = context.resources.getString(R.string.article_introvable, ""),
            barCodeInfo = barCodeInfo,
            isAutoScanMode = isAutoScanMode,
            articleMapByBarCode = articleMapByBarCode,
            tvaList = tvaList,
            useSalePrice = !isProInventory,
            onBarCodeInfo = {
                barCodeViewModel.onBarCodeInfo(barCode = it)
                articlesViewModel.onSearchValueChange(TextFieldValue(""))
            },
            showToast = { message, type ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  type,
                )

            }
        )
    }





    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                showNavIcon = !articlesViewModel.showSearchView && searchTextState.text.isEmpty(),
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.select_articles),
                titleVisibilty = !articlesViewModel.showSearchView && searchTextState.text.isEmpty(),
                //  showNavIcom = clientViewModel.searchTextState.text.isBlank(),

                actions = {
                    SearchSectionComposable(
                        label = context.getString(R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList.first()
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]}),
                        searchVisibility  = articlesViewModel.showSearchView || searchTextState.text.isNotEmpty(),
                        // requestFocus = mainViewModel.selectedClient == Client(),
                        searchTextState = searchTextState,
                        onSearchValueChange = {
                            articlesViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            articlesViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            articlesViewModel.onShowCustomFilterChange(it)
                        }
                    )
                }
            )
        },
        bottomBar = {
            AddViewBottomAppBar(
                haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                toaster = toaster,
                showSaveBtn = selectedArticleList.isNotEmpty(),
                showAddArticleBtn = false,
             //   showPriceCategori = haveChoosePriceCategoriAuthorisation,
                onSaveClick = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectArtCalculVM.setSelectedArticll(SelectedArticle())
                    popBackStack()
                },
                onClickAddArticle = {
                },
                isAutoScanMode = isAutoScanMode,
                setAutoScanMode = {
                    mainViewModel.setAutoAddMode(!isAutoScanMode)
                },
                openBareCodeScanner  = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },
                onPriceCategoryClick = {
                    selectArtCalculVM.setShowPriceCategoryChange(true)
                },
                showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice
            )
        }

    ) { padding ->
        if(selectArtCalculVM.showPriceCategory) {
            Dialog(
                onDismissRequest = {
                    // Dismiss the dialog when the user clicks outside the dialog or on the back
                    // button. If you want to disable that functionality, simply use an empty
                    // onDismissRequest.
                    selectArtCalculVM.setShowPriceCategoryChange(false)

                },
                properties = DialogProperties(
                    usePlatformDefaultWidth = true
                ),
                content = {
                    Card(
                        elevation = CardDefaults.cardElevation(),
                        shape = RoundedCornerShape(15.dp),
                        modifier = Modifier
                    ) {

                        RadioButtonChoiceView(
                            title = stringResource(R.string.select_price_categorie),
                            onDismiss = {
                                selectArtCalculVM.setShowPriceCategoryChange(false)
                            },
                            selectedOption = selectedPriceCategory,
                            onOptionSelected =  {
                                selectArtCalculVM.onSelectedPriceCategoryChange(it)


                                /**
                                 * iF NEED TO UPDATE ALL ARTICLE PRICES
                                 * BUT IF IMPLEMNETED MUST CORRECT CRASH *** TO TEST IF IMPLEMENTED
                                 */
//                                for (selectedArt in selectedArticleList) {
//                                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
//                                        hasPromo = hasPromo,
//                                        // context = context,
//                                        operation = Globals.NO_OPERATION,
//                                        quantiti = selectArtCalculVM.getCurrentSelectdArt(article = selectedArt.article, tvaList = tvaList).quantity,
//                                        useSalePrice = !isProInventory
//                                    )
//                                }

                                selectArtCalculVM.setTotalDiscount()
                                selectArtCalculVM.totalPriceDiscount()
                            },
                            priceCategoryList = priceCategoryList
                        )

                    }

                }
            )
        }

        if(selectArtCalculVM.showSetArticle) {
            SetArticleDialogue(
                toaster = toaster,
                isProInventory = isProInventory,
                selectedArticle = selectedArticle,
                priceCategoryList = priceCategoryList,
                hasPromo = hasPromo,
                proCaisseAuthorization = proCaisseAuthorization,
                showPriceCategorySingleArticle = selectArtCalculVM.showPriceCategorySingleArticle,
                showTva = showTva,
                tvaList = tvaList,
                selectedTva = selectedArticle.tva,
                tvaExpanded = mainViewModel.tvaExpand,
                unitArticleExpanded = mainViewModel.uniteArticleExpand,
                unitArticleList = mainViewModel.uniteArticleList.filter { it.uNITEARTICLECodeArt == selectedArticle.article.aRTCode },
                onSelectedUnitArticleChange = {
                    selectArtCalculVM.setSelectedArticll(selectedArticle.copy(uniteArticle = it))
                    selectArtCalculVM.updateSelectedArticleMobilityList()
                },
                onUnitArticleExpandedChange = { mainViewModel.onUniteArticleExpandedChange(it) },
                onConfirm = {
                    if(stringToDouble(selectedArticle.quantity) <= 0.0 || stringToDouble(selectedArticle.lTMtBrutHT) <= 0.0){
                        selectArtCalculVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                        return@SetArticleDialogue
                    }
                    selectArtCalculVM.totalPriceDiscount()
                },
                onReset = {
                    selectArtCalculVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)
                },
                setShowPriceCategoryChange = {
                    selectArtCalculVM.setShowPriceCategoryChange(it)
                },
                onShowSetArticleChange= {
                    selectArtCalculVM.onShowSetArticleChange(it)
                },
                setShowPriceCategorySingleArticleChange={
                    selectArtCalculVM.setShowPriceCategorySingleArticleChange(it)
                },
                getSingleArticlePrice = {
                    selectArtCalculVM.getPrice(it.article)
                },
                onSelectedPriceCategorieChange = {
                    selectArtCalculVM.onSelectedPriceCategoryChange(it)

                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        quantiti = selectedArticle.quantity,
                        useSalePrice = !isProInventory
                    )

//                    selectArtCalculVM.setTotalDiscount()
//                    selectArtCalculVM.totalPriceDiscount()
                },
                updateQty= {

                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //  context = context,
                        operation = Globals.NO_OPERATION,
                        // barCodeViewModel = barCodeViewModel,
                        quantiti = it,
                        typeOperation = "qty",
                        useSalePrice = !isProInventory
                    )
                },
                onRemiseChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        remise = it,
                        typeOperation = "remise",
                        useSalePrice = !isProInventory
                    )
                },
                onPrixCaisseChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //     context = context,
                        operation = Globals.NO_OPERATION,
                        //   barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixCaisse",
                        prixCaisse = it,
                        useSalePrice = !isProInventory
                    )
                },
                onPrixTotalChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //    context = context,
                        operation = Globals.NO_OPERATION,
                        //   barCodeViewModel = barCodeViewModel,
                        typeOperation = "totalPrice",
                        prixtotal = it,
                        useSalePrice = !isProInventory
                    )
                },
                onPrixHTChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixAchatHT",
                        prixHT = it,
                        useSalePrice = !isProInventory
                    )
                },
                onSelectedTvaChange = {
                    //TOdo add calcul
                    //  mainViewModel.onSelectedTvaChange(it)
                    //selectArtCalculVM.setSelectedArticlTva(it)
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        tva = it,
                        typeOperation = "tva",
                        useSalePrice = !isProInventory
                    )
                },
                onTvaExpandedChange = {
                    mainViewModel.onTvaExpandedChange(it)
                },
                onPrixVenteChange = {
                    selectArtCalculVM.setSelectedArticlPrixVente(it)
                }

            )
        }

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                //  .background(colorResource(id = R.color.black))
                .fillMaxSize()
                .padding(padding)
        ) {

            if (articlesViewModel.showCustomFilter)
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.article_filter),
                    onShowCustomFilterChange = {
                        articlesViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        articlesViewModel.onEvent(event = it)
                    }
                )

            StockFilterSegmentedButton(
                onfilterByStockChange = {
                    articlesViewModel.onSearchValueChange(TextFieldValue(""))
                    articlesViewModel.onfilterByStockChange(it)
                },
                stockArticleStock = stockArticleStock
            )
            DataBasePaginationLoadState(lazyPagingItems = articleList)



            if ((articleList?.itemCount?:0) > 0) {
                SelectArticlesList(
                    selectedArticleList = selectedArticleList,
                    tvaList = tvaList,
                    listUnite = listUnite,
                    articleCodeBarList = articleCodeBarList,
                    filteredArticles = articleList,
                    selectArtCalculVM = selectArtCalculVM,
                    controlQuantity = controlQuantity,
                    isChahiaClient = isChahiaClient,
                    isAutoScanMode =  isAutoScanMode,
                    showTva = showTva,
                    toaster = toaster,
                    isProInventory = isProInventory
                )
            }
            else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
        }
    }

}

@Composable
fun SelectArticlesList(
    selectedArticleList: List<SelectedArticle>,
    tvaList: List<Tva>,
    listUnite: List<Unite>,
    articleCodeBarList: List<ArticleCodeBar>,
    selectArtCalculVM: SelectArticleCalculViewModel,
    isAutoScanMode : Boolean,
    toaster: ToasterState,
    filteredArticles: LazyPagingItems<Article>?,
    controlQuantity : Boolean,
    isChahiaClient : Boolean,
    isProInventory : Boolean,
    showTva: Boolean
) {

    val context = LocalContext.current
    if(filteredArticles == null) return



    LazyVerticalStaggeredGrid(
        columns = StaggeredGridCells.Adaptive(minSize = 128.dp),
        contentPadding = PaddingValues(12.dp),
        verticalItemSpacing = 16.dp,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(
            count = filteredArticles.itemCount,
            key = { filteredArticles[it]?.aRTCode?: "" }
        ) { index ->

            val selectedArticle = selectArtCalculVM.getCurrentSelectdArt(article = filteredArticles[index]?: Article(), tvaList = tvaList)


           val article = selectedArticle.article

            val articleCodeBar = articleCodeBarList.firstOrNull { it.filsCodeBar == article.aRTCodeBar }?: ArticleCodeBar(filsCodeBar = article.aRTCodeBar)


            val unite = listUnite.firstOrNull { it.uNICode == article.uNITEARTICLECodeUnite }?.uNIDesignation?: article.uNITEARTICLECodeUnite



            val articleQty = getQuantity(article = article)

           val selectedArticleQty = stringToDouble(selectedArticle.quantity)


            val price = selectArtCalculVM.getPrice(article = article, showPrixVente = showTva)

            val isChahiaClt = articleCodeBar.codQte != null && isChahiaClient
            OutlinedCard(
                border = BorderStroke(width = 1.dp, color = if(articleQty> 0) LocalContentColor.current else MaterialTheme.colorScheme.error),
                modifier = Modifier.clickable {
                            if(!article.isSync) {
                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = context.resources.getString(R.string.syncbefore_use_art),
                                    type =  ToastType.Info,
                                )
                                return@clickable
                            }

                    if(stringToDouble(price)<=0) {

                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.invalid_article_price) + "\n "+ StringUtils.convertStringToPriceFormat(price),
                            type = ToastType.Warning,
                        )

                        return@clickable
                    }


                    selectArtCalculVM.setSelectedArticll(selectedArticle)


                            if(controlQuantity && if(isChahiaClt) stringToDouble(articleCodeBar.codQte) <= 0.0 else articleQty <= 0.0) {
                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = context.resources.getString(R.string.qte_more_than_zero),
                                    type = ToastType.Error,
                                )
                                return@clickable
                            }
                    if(isAutoScanMode) {


                        if (selectedArticleList.any { it.article.aRTCode == article.aRTCode })
                            selectArtCalculVM.deleteItemToSelectedArticleMobilityList(article)
                        else selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                            operation = Globals.ADD,
                            useSalePrice = !isProInventory
                        )
                    }
                    else {
                        if(!selectArtCalculVM.isArtSelected(article = article)) {
                            selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                                operation = Globals.ADD,
                                useSalePrice = !isProInventory
                            )

                        }

                        selectArtCalculVM.onShowSetArticleChange(true)
                    }
                },
                shape = roundedCornerShape()
                ) {
                Column(
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(12.dp)
                ) {
                    if (!article.isSync)
                        LottieAnim(lotti = R.raw.connection_error, size = 100.dp)
                    else Icon(
                        imageVector = Icons.TwoTone.ShoppingCart,
                        contentDescription = "",
                        modifier = Modifier.size(100.dp)

                    )



                    Text(
                        textAlign = TextAlign.Center,
                        text = article.aRTDesignation,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                    )
                    Text(
                        textAlign = TextAlign.Center,
                        text = removeTrailingZeroInDouble(articleQty.toString()).ifEmpty { "N/A" } + " " + unite,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        maxLines = 1
                    )

                    Text(
                        textAlign = TextAlign.Center,
                        text = StringUtils.convertStringToPriceFormat(price),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                    )

                    if(article.isSync) {
                        if (if(isChahiaClt) stringToDouble(articleCodeBar.codQte) <= 0.0 else articleQty <= 0.0) {
                            if(!controlQuantity) {
                                PlusMinusSection(
                                    qty = selectedArticleQty,
                                    articleQt = selectArtCalculVM.getArticleQt(
                                        quantityInStock = articleQty,
                                        controlQuantity= false
                                    ),
                                    sARTQte = articleQty,
                                    controlQuantity = false,
                                    onMinusClicked = {
                                        selectArtCalculVM.setSelectedArticll(selectedArticle)
                                        if (selectedArticleQty - 1 <= 0.0) {
                                            selectArtCalculVM.deleteItemToSelectedArticleMobilityList(article)
                                            return@PlusMinusSection
                                        }

                                        selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                                            operation = Globals.MINUS,
                                            typeOperation = "qty",
                                            useSalePrice = !isProInventory
                                        )
                                    },
                                    onAddClicked = {
                                        if(stringToDouble(price) <= 0) {
                                            showToast(
                                                context = context,
                                                toaster = toaster,
                                                message = context.resources.getString(R.string.invalid_article_price) + "\n "+ StringUtils.convertStringToPriceFormat(price),
                                                type =  ToastType.Warning,
                                            )
                                            return@PlusMinusSection
                                        }

                                        selectArtCalculVM.setSelectedArticll(selectedArticle)

                                        selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                                            operation = Globals.ADD,
                                            typeOperation = "qty",
                                            useSalePrice = !isProInventory,
                                          //  quantiti = selectedArticleQty.toString()
                                        )
                                    }
                                )
                            }
                        }
                        else {
                            PlusMinusSection(
                                qty = selectedArticleQty,
                                articleQt = selectArtCalculVM.getArticleQt(
                                    quantityInStock = articleQty,
                                    controlQuantity= controlQuantity
                                ),
                                sARTQte = articleQty,
                                controlQuantity = controlQuantity,
                                onMinusClicked = {
                                    selectArtCalculVM.setSelectedArticll(selectedArticle)
                                    if (selectedArticleQty - 1 <= 0.0) {
                                        selectArtCalculVM.deleteItemToSelectedArticleMobilityList(article)
                                        return@PlusMinusSection
                                    }

                                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                                        operation = Globals.MINUS,
                                        typeOperation = "qty",
                                        useSalePrice = !isProInventory
                                    )
                                },
                                onAddClicked = {
                                    if(stringToDouble(price)<=0) {
                                        showToast(
                                            context = context,
                                            toaster = toaster,
                                            message = context.resources.getString(R.string.invalid_article_price) + "\n "+ StringUtils.convertStringToPriceFormat(price),
                                            type =  ToastType.Warning,
                                        )
                                        return@PlusMinusSection
                                    }

                                    selectArtCalculVM.setSelectedArticll(selectedArticle)

                                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                                        operation = Globals.ADD,
                                        typeOperation = "qty",
                                        useSalePrice = !isProInventory,
                                       // quantiti = selectedArticleQty.toString()
                                    )
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}






@Composable
fun PlusMinusSection(qty: Double,
                     articleQt: Double,
                     sARTQte: Double,
                     controlQuantity: Boolean,
                     onAddClicked:() -> Unit,
                     onMinusClicked:() -> Unit,
                     ) {
    Row(
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (qty > 0.0) {
            Icon(
                imageVector = if (qty <= 1.0)
                    Icons.Default.DeleteOutline
                else Icons.Default.RemoveCircleOutline,
                contentDescription = "",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier
                    .size(25.dp)
                    .clickable {
                        onMinusClicked()
                    }

            )



            AnimatedText(
                count = qty,
                style = MaterialTheme.typography.bodyMedium
            )

        }


        if ((qty + articleQt <= sARTQte && controlQuantity) || !controlQuantity)


            Icon(
                imageVector = Icons.Default.AddCircleOutline,
                contentDescription = "",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier
                    .size(25.dp)
                    .clickable {
                        onAddClicked()

                    }
            )

    }
}



