package com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AddCircleOutline
import androidx.compose.material.icons.filled.DeleteOutline
import androidx.compose.material.icons.filled.RemoveCircleOutline
import androidx.compose.material.icons.twotone.ShoppingCart
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.addItemToSelectedArticleListAndUpdateQty
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getArticleQt
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getQuantity
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.selectedArticle
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.setSelectedArticleNoCalcul
import com.asmtunis.procaisseinventory.articles.consultation.screens.StockFilterSegmentedButton
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.MINUS
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ui.AddNewProductDialogue
import com.asmtunis.procaisseinventory.pro_inventory.utils.SelectedArticle.addNewLigneSelectedInventoryArtcle
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.DataBasePaginationLoadState
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.text_animation.AnimatedText
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.roundedCornerShape


@ExperimentalMaterial3Api
@Composable
fun SelectArticlesScreenNoCalcul(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    stationOrigineCode: String,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    articlesViewModel: ArticlesViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    selectArtNoCalculVM : SelectArticleNoCalculViewModel
    //  searchViewModel: SearchViewModel
) {
    val context = LocalContext.current


    val searchTextState = articlesViewModel.searchTextState
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val stationOrigine = mainViewModel.stationList.firstOrNull { it.sTATCode == stationOrigineCode }?: Station(sTATCode = stationOrigineCode)
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val snackbarHostState = remember { SnackbarHostState() }

    val listUnite = mainViewModel.uniteList
    val stationStockArticlMapByBarCode = mainViewModel.stationStockArticlMapByBarCode
    val barCodeInfo = barCodeViewModel.barCodeInfo
    val articlesListState = articlesViewModel.articlesListState

    val articleList: LazyPagingItems<Article>? = articlesListState.lists?.collectAsLazyPagingItems()
    val listOrder = articlesListState.listOrder

    val listFilter = articlesListState.filter
    val isAutoScanMode = mainViewModel.isAutoScanMode
    val tvaList = mainViewModel.tvaList
    val filterList = context.resources.getStringArray(R.array.article_filter)

    val selectedArticleInventory = selectArtNoCalculVM.selectedArticleInventory

    val stockArticleStock = articlesViewModel.stockArticleStock
    val parametrage = mainViewModel.parametrage
    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val controlQuantity = selectArtNoCalculVM.controlQuantity

    val selectedArticleInventoryList = selectArtNoCalculVM.selectedArticleInventoryList
    LaunchedEffect(key1 = Unit
//        key1 = searchTextState.text,
//        key2 = articlesListState.lists,
//        key3 = articlesListState.filter
    ) {
    articlesViewModel.filterArticles(articlesListState = articlesListState)

    }

//    LaunchedEffect(key1 = stockArticleStock) {
//        if (stockArticleStock == StockArticle.SELECTED.filter)  return@LaunchedEffect
//
//            articlesViewModel.filterArticles(articlesListState = articlesListState, stationId = stationOrigineCode)
//
//    }



    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect


//stationOrigineCode
        articlesViewModel.onEvent(event = ListEvent.ListSearch(ListSearch.SecondSearch()))
        articlesViewModel.onSearchValueChange(TextFieldValue(barCodeInfo.value))

        val scannedArticle = articleMapByBarCode.values.firstOrNull{ it.aRTCodeBar == barCodeInfo.value}

        if (scannedArticle == null) {
            showToast(
                context = context,
                toaster = toaster,
                message = barCodeInfo.value + "\n" + context.resources.getString(R.string.article_introvable),
                type = ToastType.Error,
            )
            return@LaunchedEffect
        }

        val qteStation = getQuantity(article = scannedArticle)

        if (isAutoScanMode) {
            val currentSelectedArticle = selectedArticle(
                listSelectedArticleInventory = selectedArticleInventoryList,
                article = scannedArticle
            )
            if (stringToDouble(currentSelectedArticle.quantity) +
                getArticleQt(
                    controlQuantity = controlQuantity,
                    quantityInStock =qteStation
                ) <= qteStation
            ) {

               val stationStockArticle = stationStockArticlMapByBarCode[scannedArticle.aRTCode+ stationOrigineCode]?: StationStockArticle()
                 val articleStationStock = stationStockArticle?.sARTQteStation?: "0"

                addItemToSelectedArticleListAndUpdateQty(
                    listUnite = listUnite,
                    currentSelectedArticle = selectedArticle(
                        listSelectedArticleInventory = selectedArticleInventoryList,
                        article = scannedArticle
                    ),
                    article = scannedArticle,
                    stationStockArticle = stationStockArticle,
                    tva =  if (currentSelectedArticle.tva != Tva()) currentSelectedArticle.tva else tvaList.first(),
                    controlQuantity = controlQuantity,
                    stockArticleAllStation = articleStationStock,
                    addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)}

                )
            }
        } else {


            setSelectedArticleNoCalcul(
                scannedArticle = scannedArticle,
                stationOrigineCode = stationOrigineCode,
                selectedArticleInventoryList = selectedArticleInventoryList,
                stationStockArticlMapByBarCode = stationStockArticlMapByBarCode,
                setSelectedArticlInventory = { value: SelectedArticle, from: String ->
                selectArtNoCalculVM.setSelectedArticlInventory(value = value, from = from)
            }
            )
            mainViewModel.setAddNewProductDialogueVisibility(true)
        }


    }



    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                showNavIcon = !articlesViewModel.showSearchView && searchTextState.text.isEmpty(),
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.select_articles),
                titleVisibilty = !articlesViewModel.showSearchView && searchTextState.text.isEmpty(),
                //  showNavIcom = clientViewModel.searchTextState.text.isBlank(),

                actions = {
                    SearchSectionComposable(
                        label = context.getString(R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList.first()
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]}),
                        searchVisibility  = articlesViewModel.showSearchView || searchTextState.text.isNotEmpty(),
                       // requestFocus = mainViewModel.selectedClient == Client(),
                        searchTextState = searchTextState,
                        onSearchValueChange = {
                            articlesViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            articlesViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            articlesViewModel.onShowCustomFilterChange(it)
                        }
                    )
                }
            )
        },

        bottomBar = {
            AddViewBottomAppBar(
                haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                toaster = toaster,
                showSaveBtn = selectedArticleInventoryList.isNotEmpty(),
                onSaveClick = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())

                   // selectArtInventoryVM.resetSelectedInventoryArticles()

                    popBackStack()
                },
                onClickAddArticle = {
                },
                isAutoScanMode = isAutoScanMode,
                setAutoScanMode = {
                    mainViewModel.setAutoAddMode(!isAutoScanMode)
                },
                openBareCodeScanner  = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },
                showAddArticleBtn = false,
                showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice
            )
        }
    ) { padding ->
        if (mainViewModel.openAddNewProductDialogue) {

            AddNewProductDialogue(
                prixLabel= if (stationOrigineCode.isBlank()) stringResource(R.string.price_title) else stringResource(R.string.purchase_price_title) ,
                showTvaMenu = false,
                canModify = true,
                station = stationOrigine,
                selectedArticleInventory = selectedArticleInventory,
                tvaExpand = mainViewModel.tvaExpand,
                uniteArticleExpand = mainViewModel.uniteArticleExpand,
                tvaList = mainViewModel.tvaList,
                uniteArticleList = mainViewModel.uniteArticleList,

                onTvaExpandedChange = {
                    mainViewModel.onTvaExpandedChange(it)
                },
                onUniteArticleExpandedChange = {
                    mainViewModel.onUniteArticleExpandedChange(it)
                },
                setSelectedArticlInventory = {
                    selectArtNoCalculVM.setSelectedArticlInventory(it, from = "6")
                    //  selectArtInventoryVM.addItemToSelectedArticleInventoryList(it)
                },

                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectArtNoCalculVM.setSelectedArticlInventory(SelectedArticle(), from = "fdds")
                    mainViewModel.setAddNewProductDialogueVisibility(false)
                },
                onConfirm = {
                    addNewLigneSelectedInventoryArtcle(
                        toaster = toaster,
                        context = context,
                        fromScan = false,
                        selectedArticle = selectedArticleInventory,
                        setSelectedArticlInventory = {
                            selectArtNoCalculVM.setSelectedArticlInventory(it)
                        },
                        addItemToSelectedArticleInventoryList = {
                            //  Log.d("rdgdfgdsggd", "addItemToSelectedArticleInventoryList "+ it.quantity)
                            selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)
                        },
                        resetBarCode = { barCodeViewModel.onBarCodeInfo(barCode = BareCode()) },
                        isAutoScanMode = isAutoScanMode,
                        tva = if (selectedArticleInventory.tva == Tva()) mainViewModel.tvaList.first()
                        else selectedArticleInventory.tva,
                        controlQuantity = controlQuantity,
                        station = stationOrigine,
                        stationStockArticl = stationStockArticlMapByBarCode,
                    )

                    //  barCodeViewModel.onBarCodeInfo(BareCode())
                    // selectArtInventoryVM.setSelectedArticlInventory(SelectedArticle(), from = "f")
                    mainViewModel.setAddNewProductDialogueVisibility(false)
                }
            )
        }

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {

            if (articlesViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.article_filter),
                    onShowCustomFilterChange  = {
                        articlesViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        articlesViewModel.onEvent(event = it)
                    }
                )
            }





            AnimatedVisibility(
                visible = !( articlesViewModel.showSearchView),
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                StockFilterSegmentedButton(
                    onfilterByStockChange = {
                      //  articlesViewModel.onSearchValueChange(TextFieldValue(""))
                        articlesViewModel.filterArticles(articlesListState = articlesListState)
                        articlesViewModel.onfilterByStockChange(it)
                    },
                    stockArticleStock = stockArticleStock
                )
            }

            DataBasePaginationLoadState(lazyPagingItems = articleList)
//            if(isLoadingFromDB && searchTextState.text.isBlank()) {
//                LottieAnim(lotti = R.raw.loading, size = 150.dp)
//            }
            if ((articleList?.itemCount?: 0) > 0) {
                SelectArticlesList(
                    listUnite = listUnite,
                    isAutoScanMode = isAutoScanMode,
                    stationOrigineCode = stationOrigineCode,
                    filteredArticles = articleList,
                    selectArtNoCalculVM = selectArtNoCalculVM,
                    parametrage = parametrage,
                    toaster = toaster,
                    controlQuantity = controlQuantity,
                    tvaList = tvaList,
                    selectedArticleInventoryList = selectedArticleInventoryList,
                    stationStockArticlMapByBarCode = stationStockArticlMapByBarCode,
                    setAddNewProductDialogueVisibility = { mainViewModel.setAddNewProductDialogueVisibility(it) }
                )
            }
            else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
        }
    }

}

@Composable
fun SelectArticlesList(
    listUnite: List<Unite>,
    tvaList: List<Tva>,
    stationOrigineCode: String,
    isAutoScanMode : Boolean,
    toaster: ToasterState,
    selectedArticleInventoryList: List<SelectedArticle>,
    selectArtNoCalculVM : SelectArticleNoCalculViewModel,
    filteredArticles: LazyPagingItems<Article>?,
    stationStockArticlMapByBarCode: Map<String, StationStockArticle>,
    parametrage: Parametrages,
    controlQuantity: Boolean,
    setAddNewProductDialogueVisibility:(Boolean)-> Unit
) {

    val context = LocalContext.current
    val isProMode = parametrage.proModeM

    if(filteredArticles == null) return
    LazyVerticalStaggeredGrid(
        columns = StaggeredGridCells.Adaptive(minSize = 128.dp),
        contentPadding = PaddingValues(12.dp),
        verticalItemSpacing = 16.dp,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(
            count = filteredArticles.itemCount,
            key = {
                filteredArticles[it]?.aRTCode?: ""
            }
        ) { index ->
            val article = filteredArticles[index]!!

            val unite = listUnite.firstOrNull { it.uNICode == article?.uNITEARTICLECodeUnite }?.uNIDesignation?: article?.uNITEARTICLECodeUnite

         //   val unite = articleList[index].unite?.uNIDesignation?: article.uNITEARTICLECodeUnite

          //  val listStationStockArticl = filteredArticles[index].stationStockArticle
            val listStationStockArticl = stationStockArticlMapByBarCode[article.aRTCode + stationOrigineCode]?: StationStockArticle()
            val currentSelectedArticle = selectedArticle(
                listSelectedArticleInventory = selectedArticleInventoryList,
                article = article
            )

            val articleQty = getQuantity(article = article)
//            val qte = if(stationOrigineCode.isNotEmpty())
//                mainViewModel.listStationStockArticl.firstOrNull { it.sARTCodeArt == filteredArticles[index].aRTCode && it.sARTCodeSatation == stationOrigineCode }?.sARTQteStation?: "0"
//                else artQte.toString()

            OutlinedCard(
                border = BorderStroke(width = 1.dp, color = if(articleQty > 0) LocalContentColor.current else MaterialTheme.colorScheme.error),

                modifier = Modifier.clickable {
                    if(!article.isSync) {
                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.syncbefore_use_art),
                            type =  ToastType.Info,
                        )
                        return@clickable
                    }

                    if ((isAutoScanMode && articleQty > 0.0 && controlQuantity)  || (isAutoScanMode && articleQty > 0.0)/*|| !controlQuantity*/) {
                        if (selectedArticleInventoryList.any { it.article.aRTCode == article.aRTCode })
                            selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(article)
                        else {
                            addItemToSelectedArticleListAndUpdateQty(
                                listUnite = listUnite,
                                currentSelectedArticle = selectedArticle(
                                    listSelectedArticleInventory = selectedArticleInventoryList,
                                    article = article
                                ),
                                article = filteredArticles[index]?: Article(),
                                stationStockArticle = listStationStockArticl,
                                tva =  if (currentSelectedArticle.tva != Tva()) currentSelectedArticle.tva
                                else tvaList.first(),
                                controlQuantity = controlQuantity,
                                stockArticleAllStation = articleQty.toString(),
                                addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)}
                            )

                        }
                    }
                    else {
//                      val selectedArt = selectedArticleInventoryList.firstOrNull { it.article.aRTCode == article.aRTCode }
//
//                        if(selectedArt == null) {
//                            val value = SelectedArticle(
//                                article = article,
//                                quantity = "1",
//                                prixAchatHt = article.pvttc.toString(),
//                                qteStationFromDB = articleStationStock
//                            )
//                            selectArtNoCalculVM.setSelectedArticlInventory(value = value, from = "6 SelectedArticle ")
//                        }else {
//                            selectArtNoCalculVM.setSelectedArticlInventory(selectedArt, from = "6 selectedArt ")
//                        }


                        setSelectedArticleNoCalcul(
                            scannedArticle = article,
                            stationOrigineCode = stationOrigineCode,
                            selectedArticleInventoryList = selectedArticleInventoryList,
                            stationStockArticlMapByBarCode = stationStockArticlMapByBarCode,
                            setSelectedArticlInventory = { value: SelectedArticle, from: String ->
                                selectArtNoCalculVM.setSelectedArticlInventory(value = value, from = from)
                            }
                        )
                        setAddNewProductDialogueVisibility(true)
                    }

                },
                shape = roundedCornerShape(),

                ) {
                Column(
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(12.dp)


                ) {


                    if (!article.isSync) {
                        LottieAnim(lotti = R.raw.connection_error, size = 100.dp)
                    } else  Box(
                        modifier = Modifier.size(100.dp),
                        contentAlignment = Alignment.Center
                    ){
                        Icon(
                            imageVector = Icons.TwoTone.ShoppingCart,
                            contentDescription = "",

                            modifier = Modifier.size(100.dp)

                            // .padding(5.dp)
                        )
                        if(isProMode == 1 && article.cOUDesignation!= null) {
                            Card() {
                                Text(
                                    modifier = Modifier.padding(start = 6.dp, end = 6.dp, top = 3.dp, bottom = 3.dp),
                                    text = article.cOUDesignation?:"N/A"
                                )
                            }

                        }
                    }





                    Text(
                        textAlign = TextAlign.Center,
                        text = article.aRTDesignation + if (isProMode == 1 && article.taiTaille.isNotEmpty())" (${article.taiTaille})"  else "" ,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                    )
                    Text(
                        textAlign = TextAlign.Center,
                        //  modifier = Modifier.width(90.dp),
                        text = StringUtils.removeTrailingZeroInDouble(articleQty.toString()).ifEmpty { "N/A" } + " " + unite,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        //   color = Color.Black,
                        maxLines = 1
                    )

                    Text(
                        textAlign = TextAlign.Center,
                        text = StringUtils.convertStringToPriceFormat(article.pvttc.toString()),
                        // text = filteredArticles[index].pvttc.toString() ,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        //  color = Color.Black
                    )

                    if (article.isSync) {


                    if ((articleQty > 0 && controlQuantity) || !controlQuantity) {
                        Row(
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            if (currentSelectedArticle.quantity.isNotEmpty()) {
                                Icon(
                                    imageVector = if (stringToDouble(currentSelectedArticle.quantity) <= 1.0)
                                        Icons.Default.DeleteOutline
                                    else Icons.Default.RemoveCircleOutline,
                                    contentDescription = "",
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier
                                        .clickable {
                                            if (stringToDouble(currentSelectedArticle.quantity) - 1 <= 0.0) {
                                                selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(
                                                    article
                                                )
                                                return@clickable
                                            }
                                            addItemToSelectedArticleListAndUpdateQty(
                                                listUnite = listUnite,
                                                currentSelectedArticle = selectedArticle(
                                                    listSelectedArticleInventory = selectedArticleInventoryList,
                                                    article = article
                                                ),
                                                article = filteredArticles[index]?: Article(),
                                                stationStockArticle = listStationStockArticl,
                                                tva = if (currentSelectedArticle.tva != Tva()) currentSelectedArticle.tva
                                                else tvaList.first(),
                                                operation = MINUS,
                                                controlQuantity = controlQuantity,
                                                stockArticleAllStation = articleQty.toString(),
                                                addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)}
                                            )
                                        }
                                        .size(25.dp)
                                    // .padding(5.dp)
                                )



                                AnimatedText(
                                    count = stringToDouble(currentSelectedArticle.quantity),
                                    style = MaterialTheme.typography.bodyMedium
                                )

                            }


                            if ((stringToDouble(currentSelectedArticle.quantity) +
                                        getArticleQt(controlQuantity = controlQuantity,quantityInStock = getQuantity(article = currentSelectedArticle.article)) <=
                                        articleQty && controlQuantity) || !controlQuantity
                            )


                                Icon(
                                    imageVector = Icons.Default.AddCircleOutline,
                                    contentDescription = "",
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.clickable {
                                            addItemToSelectedArticleListAndUpdateQty(
                                                listUnite = listUnite,
                                                currentSelectedArticle = selectedArticle(
                                                    listSelectedArticleInventory = selectedArticleInventoryList,
                                                    article = article
                                                ),
                                                article = filteredArticles[index]?: Article(),
                                                stationStockArticle = listStationStockArticl,
                                                tva = if (currentSelectedArticle.tva != Tva()) currentSelectedArticle.tva
                                                else tvaList.first(),
                                                controlQuantity = controlQuantity,
                                                stockArticleAllStation = articleQty.toString(),
                                                addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)}
                                            )
                                            //   }
                                        }
                                        .size(25.dp)
                                )

                        }
                    }
                    }

                }
            }


        }
    }
}




















