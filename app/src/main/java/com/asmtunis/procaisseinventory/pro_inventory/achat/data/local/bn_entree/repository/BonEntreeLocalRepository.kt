package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.repository

import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import kotlinx.coroutines.flow.Flow



interface BonEntreeLocalRepository {
    fun upsertAll(value: List<BonEntree>)
    fun upsert(value: BonEntree)
    fun deleteAll()

    fun delete(bonEntree: BonEntree)
    fun getAll(): Flow<List<BonEntree>>

    fun count(): Flow<Int>
    fun getNotSync(): Flow<List<BonEntree>>

    fun noSynced(): Flow<Map<BonEntree, List<LigneBonEntree>>>
    fun getNewCode(prefix: String): Flow<String>

    fun  updateBonEntreeStatus(bonEntNum: String, bonEntNumM: String, exercice: String)
    fun getAllFiltred(isAsc: Int, filterByStationEntree: String, filterByFournisseur: String, sortBy: String)
    : Flow<Map<BonEntree, List<LigneBonEntree>>>

    fun filterByBonEntNum(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int)
    : Flow<Map<BonEntree, List<LigneBonEntree>>>

    fun filterByBonEntNumPiece(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int)
    : Flow<Map<BonEntree, List<LigneBonEntree>>>


    fun filterByBonEntCodeFrs(searchString: String, filterByStationEntree: String, filterByFournisseur: String, sortBy: String, isAsc: Int)
    : Flow<Map<BonEntree, List<LigneBonEntree>>>


}