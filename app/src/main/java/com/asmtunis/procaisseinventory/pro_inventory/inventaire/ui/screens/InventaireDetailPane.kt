package com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.view_model.InventairePrintViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ui.AddNewProductDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableHeader
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column.FourColumnTable
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import java.util.Locale


@Composable
fun InventaireDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    inventaireViewModel: InventaireViewModel,
    dataViewModel: DataViewModel,
    bluetoothVM: BluetoothViewModel,
    selectArtInventoryVM : SelectArticleNoCalculViewModel,
    printViewModel: PrintViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    settingViewModel: SettingViewModel,
    sunmiPrintManager: SunmiPrintManager,
    inventairePrintViewModel: InventairePrintViewModel
) {

    val context = LocalContext.current
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val parametrage =mainViewModel.parametrage
    val inventaire = inventaireViewModel.selectedInventaire
    val ligneInventaire = inventaireViewModel.selectedListLgInventaire

    val ligneInventaireState = getProInventoryDataViewModel.ligneInventaireState

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val fiterValue = inventaireViewModel.fiterValue


   val selectedArticleInventory = selectArtInventoryVM.selectedArticleInventory
    val stationList = mainViewModel.stationList

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val selectedArticleInventoryList =  selectArtInventoryVM.selectedArticleInventoryList




    val stationStockArticlMapByBarCode = mainViewModel.stationStockArticlMapByBarCode



    LaunchedEffect(key1 = ligneInventaireState) {
        if(!ligneInventaireState.loading && ligneInventaire.isEmpty() && ligneInventaireState.error == null && !ligneInventaireState.data.isNullOrEmpty()) {
            inventaireViewModel.onSelectedLigneInventaireChange(ligneInventaireState.data)

            selectArtInventoryVM.setSelectedList(
                listLigneInventaire = ligneInventaireState.data,
                articleMapByBarCode = articleMapByBarCode,
                stationStockArticl = stationStockArticlMapByBarCode
            )

        }

    }
    LaunchedEffect(key1 = Unit) {
        if (printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            PrintFunctions.print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = sunmiPrintManager,
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printInventaire(
                        context = context,
                        lgInventaire = ligneInventaire,
                        inventaire = inventaire,
                        printParams = printParams,
                        station = stationList.first { it.sTATCode == inventaire.iNVCodeStation },
                        articleMapByBarCode = articleMapByBarCode
                    )
                },
                toPrintSunmi = {
                    // Use the SunmiPrintManager directly
                    val utilisateur = mainViewModel.utilisateur
                    sunmiPrintManager.printInventaire(
                        context = context,
                        inventaire = inventaire,
                        lgInventaire = ligneInventaire,
                        articleMapByBarCode = articleMapByBarCode,
                        station = stationList.first { it.sTATCode == inventaire.iNVCodeStation },
                        utilisateur = utilisateur,
                        printParams = printParams
                    )
                }
            )

        }
    }


        LaunchedEffect(key1 = selectedArticleInventoryList, key2 = selectedArticleInventory) {
            val station = stationList.firstOrNull { it.sTATCode == inventaire.iNVCodeStation }?: Station()
        inventaireViewModel.onSelectedStationChange(station)


    }



        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                       popBackStack()
                    },
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = inventaire.iNVCode,
                    actions = {
                                IconButton(
                                    onClick = {
                                        PrintFunctions.print(
                                            context = context,
                                            toaster = toaster,
                                            navigate = { navigate(it) },
                                            printViewModel = printViewModel,
                                            bluetoothVM = bluetoothVM,
                                            sunmiPrintManager = sunmiPrintManager,
                                            printParams = printParams,
                                            toPrintBT = {
                                                printViewModel.printInventaire(
                                                    context = context,
                                                    lgInventaire = ligneInventaire,
                                                    inventaire = inventaire,
                                                    printParams = printParams,
                                                    station = stationList.first { it.sTATCode == inventaire.iNVCodeStation },
                                                    articleMapByBarCode = articleMapByBarCode
                                                )
                                            },
                                            toPrintSunmi = {
                                                // Use the SunmiPrintManager directly
                                                val utilisateur = mainViewModel.utilisateur
                                                sunmiPrintManager.printInventaire(
                                                    context = context,
                                                    inventaire = inventaire,
                                                    lgInventaire = ligneInventaire,
                                                    articleMapByBarCode = articleMapByBarCode,
                                                    station = stationList.first { it.sTATCode == inventaire.iNVCodeStation },
                                                    utilisateur = utilisateur,
                                                    printParams = printParams
                                                )
                                            }
                                        )
                                    }) {
                                    Icon(
                                        imageVector = Icons.TwoTone.Print,
                                        contentDescription = stringResource(id = R.string.print)
                                    )
                                }
                    }
                )
            }

        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }

            if (mainViewModel.openAddNewProductDialogue) {

                AddNewProductDialogue(
                    showTvaMenu = false,
                    prixLabel= stringResource(R.string.price_title),
                    canModify = false,
                    selectedArticleInventory = selectedArticleInventory,
                    tvaExpand = mainViewModel.tvaExpand,
                    tvaList = mainViewModel.tvaList,
                    onTvaExpandedChange = {
                        mainViewModel.onTvaExpandedChange(it)
                    },
                    setSelectedArticlInventory = {
                        selectArtInventoryVM.setSelectedArticlInventory(it)
                    },
                    onDismiss = {
                        mainViewModel.setAddNewProductDialogueVisibility(false)
                    }
                )
            }


            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {



                GenericDropdownMenu (
                    modifier = Modifier.customWidth(0.95f),
                    designation = inventaireViewModel.station.sTATDesg,
                    errorValue =  inventaireViewModel.stationError?.asString(),
                    label = stringResource(R.string.station),
                    readOnly = false,
                    itemList = stationList,
                    itemExpanded = mainViewModel.stationExpand,
                    selectedItem = inventaireViewModel.station,
                    getItemTrailing = { it.sTATDesg },
                    getItemDesignation = { it.sTATCode },
                    lottieAnimEmpty = {
                        LottieAnim(lotti = R.raw.emptystate)
                    },
                    lottieAnimError = {
                        LottieAnim(lotti = R.raw.connection_error, size = it)
                    }
                )
                Spacer(modifier = Modifier.height(12.dp))

                TableHeader(
                    onClickShowCalendar = {
                        mainViewModel.onShowDatePickerChange(true)
                    },
                    date = inventaire.iNVDateFormatted,
                    canModify = false,
                    selectedDateTime = mainViewModel.getSelectedDateTime()
                )


                Spacer(modifier = Modifier.height(20.dp))



if(ligneInventaireState.error != null) {
    Spacer(modifier = Modifier.height(12.dp))
    Text(
        text = ligneInventaireState.error,
        color = MaterialTheme.colorScheme.error
    )
    Spacer(modifier = Modifier.height(12.dp))
    LottieAnim(lotti = R.raw.network_error, size = 250.dp)

}
             else  if(ligneInventaireState.loading)//todo handle error case
                    LottieAnim(lotti = R.raw.loading, size = 150.dp)
                else
                FourColumnTable(// TODO MAKE LigneArticleTableInventaireInventory LIKE FourColumnTable
                    showFilterLine = selectedArticleInventoryList.size>9 && inventaireViewModel.showFilterLine,
                    onShowFilterLineChange = {
                        inventaireViewModel.onShowFilterLineChange(it)
                    },
                   fiterValue = inventaireViewModel.fiterValue,
                   onFilterValueChange = { inventaireViewModel.onFilterValueChange(it) },
                    canModify = false,
                    rowTitls  = context.resources.getStringArray(R.array.threeColumnTable_inventaire_array).toList(),
                    // client = bonCommandeVM.selectedBonCommandeWithClient.client?.cLINomPren?:bonCommandeVM.selectedBonCommandeWithClient.bonCommande?.dEVClientName?:mainViewModel.selectedClient.cLINomPren,//mainViewModel.selectedClient.cLINomPren,
                    selectedListArticle = if(fiterValue.isNotEmpty()) selectedArticleInventoryList.filter { it.article.aRTCode.lowercase(Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT))  || it.article.aRTDesignation.lowercase(Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) } else selectedArticleInventoryList,
                    onLongPress = {},
                    onTap = {
                        selectArtInventoryVM.setSelectedArticlInventory(it)
                        mainViewModel.setAddNewProductDialogueVisibility(true)
                    },
                    firstColumn = { item->
                        TableTextUtils.firstColumn(
                            selectedArticle = item,
                            parametrage = parametrage,
                            articleMapByBarCode = articleMapByBarCode
                        )
                    },
                    secondColumn = {
                        StringUtils.removeTrailingZeroInDouble(it.qteStationFromDB).ifEmpty { "0" }
                    },
                    thirdColumn = {
                        StringUtils.removeTrailingZeroInDouble(it.quantity).ifEmpty { "0" }
                    },
                    infoText = {
                        if(it.article.aRTQteStock.isNotEmpty())
                            context.getString(R.string.qte_in_all_stations, StringUtils.removeTrailingZeroInDouble(it.article.aRTQteStock).ifEmpty { "0" }) + " "+ it.unite.uNIDesignation
                        else ""
                    }
                )
             /*   if (state.listSelectedArticleError != null && selectedArticleInventoryList.isEmpty())
                    Text(
                        text = state.listSelectedArticleError,
                        color = MaterialTheme.colorScheme.error,
                        fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                        // modifier = Modifier.align(Alignment.Start)
                    )
*/

            }


        }




}





