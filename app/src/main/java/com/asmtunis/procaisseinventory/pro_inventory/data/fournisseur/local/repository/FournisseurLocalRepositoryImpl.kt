package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.repository

import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.dao.FournisseurDAO
import kotlinx.coroutines.flow.Flow


class FournisseurLocalRepositoryImpl(
    private val fournisseurDAO: FournisseurDAO
) : FournisseurLocalRepository {
    override fun upsertAll(value: List<Fournisseur>) =
        fournisseurDAO.insertAll(value)

    override fun upsert(value: Fournisseur) =
        fournisseurDAO.insert(value)

    override fun deleteAll() =
        fournisseurDAO.deleteAll()

    override fun getAll(): Flow<List<Fournisseur>> =
        fournisseurDAO.all

}