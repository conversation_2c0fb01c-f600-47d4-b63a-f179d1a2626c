package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.bn_livraison

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.AddBatchBonLivraisonResponse
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import kotlinx.coroutines.flow.Flow


interface BonLivraisonApi {



        suspend fun getBonLivraison(baseConfig: String): Flow<DataResult<List<BonLivraison>>>
        suspend fun addBatchBonLivraisonWithLines(baseConfig: String): Flow<DataResult<List<AddBatchBonLivraisonResponse>>>
}