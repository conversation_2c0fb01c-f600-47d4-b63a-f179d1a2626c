package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult


class ValidateList {

    fun execute(list: List<Any>?): ValidationResult {
        if (list.isNullOrEmpty()) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.list_cant_be_empty)
            )
        }
        return ValidationResult(
            successful = true,
          //  errorMessage = null
        )
    }
}