package com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues

object AuthorizationValuesProInventory {
    const val INEVENTORY_DRAWER_ITEM = "420000000"
    const val TRANSFERT_DRAWER_ITEM = "430000000"
    const val PURCHASE_DRAWER_ITEM = "440000000"
    const val SCAN_BARECODE = "450000000"
    const val ARTICLE_DRAWER_ITEM = "460000000"
    const val TICKET_RAYON_DRAWER_ITEM = "470000000"
    const val AUTO_SYNC = "520000000"
    const val ADD_ARTICLE = "800000000"

}