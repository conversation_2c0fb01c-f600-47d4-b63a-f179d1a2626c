package com.asmtunis.procaisseinventory.core.connectivity.location.data

import android.annotation.SuppressLint
import android.app.Application
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.os.Build
import androidx.annotation.RequiresApi
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils.hasLocationPermissions
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils.isGpsEnabled
import com.asmtunis.procaisseinventory.core.connectivity.location.domain.LocationTracker
import com.asmtunis.procaisseinventory.core.connectivity.location.model.Adresse
import com.google.android.gms.location.FusedLocationProviderClient
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.Locale
import javax.inject.Inject
import kotlin.coroutines.resume

class DefaultLocationTracker
    @Inject
    constructor(
        private val locationClient: FusedLocationProviderClient,
        private val application: Application,
    ) : LocationTracker {
        @SuppressLint("MissingPermission")
        override suspend fun getCurrentLocation(): Pair<Location?, UiText?> {



            if (!hasLocationPermissions(application) || !isGpsEnabled(application)) {
                return Pair(null, UiText.StringResource(R.string.permission_denied))
            }

            return suspendCancellableCoroutine { continuation ->
                locationClient.lastLocation.apply {
                    if (isComplete) {
                        if (isSuccessful) {
                            continuation.resume(Pair(result, null))
                        } else {
                            continuation.resume(
                                Pair(
                                    null,
                                    UiText.DynamicString(" 1"),// UiText.StringResource(R.string.gps_coordonate_invalid)
                                ),
                            )
                        }
                        return@suspendCancellableCoroutine
                    }
                    addOnSuccessListener {
                        if (it == null) {
                            continuation.resume(Pair(null, UiText.DynamicString("Unknown GPS Error: 2")))
                        } else {
                            continuation.resume(Pair(it, null))
                        }
                    }
                    addOnFailureListener {
                        continuation.resume(Pair(null, UiText.DynamicString(it.message)))
                    }
                    addOnCanceledListener {
                        continuation.cancel()
                    }
                }
            }
        }
    override suspend fun getCurrentAdress(
        latitude: Double,
        longitude: Double,
    ): Pair<Adresse?, UiText?>  {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            getAddressTiramisu(latitude, longitude)
        } else {
            getAddressLegacy(latitude, longitude)
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun getAddressTiramisu(latitude: Double, longitude: Double): Pair<Adresse?, UiText?> {
        return suspendCancellableCoroutine { continuation ->
            Geocoder(application, Locale.getDefault()).getFromLocation(
                latitude,
                longitude,
                1,
                object : Geocoder.GeocodeListener {
                    override fun onGeocode(addresses: MutableList<Address>) {
                        val address = addresses.firstOrNull()?.let {
                            Adresse(
                                city = it.locality ?: it.subLocality ?: it.subAdminArea
                                ?: it.adminArea,
                                state = it.adminArea,
                                country = it.countryName,
                                postalCode = it.postalCode,
                                knownName = it.featureName,
                            )
                        }
                        continuation.resume(Pair(address, null))
                    }

                    override fun onError(errorMessage: String?) {
                        continuation.resume(Pair(null, UiText.DynamicString(errorMessage)))
                    }
                }
            )
        }

    }

    private suspend fun getAddressLegacy(latitude: Double, longitude: Double): Pair<Adresse?, UiText?> {
        return try {
            val address = Geocoder(application, Locale("fr"))
                .getFromLocation(latitude, longitude, 1)
                ?.firstOrNull()
                ?.let {
                    Adresse(
                        city = it.locality ?: it.subLocality ?: it.subAdminArea ?: it.adminArea,
                        state = it.adminArea ?: "",
                        country = it.countryName ?: "",
                        postalCode = it.postalCode ?: "",
                        knownName = it.featureName ?: "",
                    )
                }
            Pair(address, null)
        } catch (e: Exception) {
            Pair(null, UiText.DynamicString(e.message))
        }
    }




      /*  override suspend fun getCurrentAdress(
            latitude: Double,
            longitude: Double,
        ): Pair<Adresse?, UiText?> {
            var adress: Adresse

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Geocoder(application, Locale(Locale.getDefault().language)).getFromLocation(
                    latitude,
                    longitude,
                    1,
                    object : Geocoder.GeocodeListener {
                        override fun onGeocode(addresses: MutableList<Address>) {
                            adress =
                                Adresse(
                                    city =
                                        addresses.firstOrNull()!!.locality
                                            ?: addresses.firstOrNull()!!.subLocality
                                            ?: addresses.firstOrNull()!!.subAdminArea
                                            ?: addresses.firstOrNull()!!.adminArea,
                                    // addresses.firstOrNull()!!.locality,
                                    state = addresses.firstOrNull()!!.adminArea,
                                    country = addresses.firstOrNull()!!.countryName,
                                    postalCode = addresses.firstOrNull()!!.postalCode,
                                    knownName = addresses.firstOrNull()!!.featureName,
                                )

                            Pair(adress, null)
                        }

                        override fun onError(errorMessage: String?) {
                            super.onError(errorMessage)

                            Pair(null, UiText.DynamicString(errorMessage))
                        }
                    },
                )
            }
            try {
                val adresse =
                    Geocoder(application.baseContext, Locale("fr"))
                        .getFromLocation(
                            latitude,
                            longitude,
                            1,
                        )?.firstOrNull()

                if (adresse != null) {
                    // do your logic
                    val city: String = // GetAdresse.checkIfNull(adresse.locality)
                        adresse.locality ?: adresse.subLocality ?: adresse.subAdminArea
                            ?: adresse.adminArea
                    val state: String = checkIfNull(adresse.adminArea)
                    val country: String = checkIfNull(adresse.countryName)
                    val postalCode: String = "" // checkIfNull(addresses!![0].postalCode)
                    val knownName: String =
                        checkIfNull(adresse.featureName) // Only if available else return NULL

                    adress =
                        Adresse(
                            city = city,
                            state = state,
                            country = country,
                            postalCode = postalCode,
                            knownName = knownName,
                        )

                    return Pair(adress, null)
                } else {
                    return Pair(null, UiText.DynamicString("Unkonw GPS Error: getCurrentAdress"))
                }
            } catch (e: Exception) {
                // will catch if there is an internet problem
                return Pair(null, UiText.DynamicString(e.message))
            }
        }*/





    }
