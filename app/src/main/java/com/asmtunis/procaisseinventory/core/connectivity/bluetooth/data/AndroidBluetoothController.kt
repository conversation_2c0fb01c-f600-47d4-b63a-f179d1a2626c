package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.data

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothController
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothDeviceDomain
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.ConnectionResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.IOException
import java.util.UUID

@SuppressLint("MissingPermission")
class AndroidBluetoothController(
    private val context: Context,
) : BluetoothController {
    private val bluetoothManager by lazy {
        context.getSystemService(BluetoothManager::class.java)
    }
    private val bluetoothAdapter by lazy {
        bluetoothManager?.adapter
    }

    private val _isConnected = MutableStateFlow(false)
    override val isConnected: StateFlow<Boolean>
        get() = _isConnected.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    override val isScanning: StateFlow<Boolean>
        get() = _isScanning.asStateFlow()

    private val _isConnecting = MutableStateFlow(false)
    override val isConnecting: StateFlow<Boolean>
        get() = _isConnecting.asStateFlow()

    private val _scannedDevices = MutableStateFlow<List<BluetoothDeviceDomain>>(emptyList())
    override val scannedDevices: StateFlow<List<BluetoothDeviceDomain>>
        get() = _scannedDevices.asStateFlow()

    private val _pairedDevices = MutableStateFlow<List<BluetoothDeviceDomain>>(emptyList())
    override val pairedDevices: StateFlow<List<BluetoothDeviceDomain>>
        get() = _pairedDevices.asStateFlow()

    private val _errors = MutableSharedFlow<String>()
    override val errors: SharedFlow<String>
        get() = _errors.asSharedFlow()

    private val foundDeviceReceiver =
        FoundDeviceReceiver { device ->
            _scannedDevices.update { devices ->
                val newDevice = device.toBluetoothDeviceDomain()
                if (newDevice in devices || pairedDevices.value.contains(newDevice)) devices else devices + newDevice
            }
        }

    private val bluetoothStateReceiver =
        BluetoothStateReceiver { isConnected, bluetoothDevice ->
            if (bluetoothAdapter?.bondedDevices?.contains(bluetoothDevice) == true) {
                _isConnected.update { isConnected }
            } else {
                CoroutineScope(Dispatchers.IO).launch {
                    _errors.emit("Can't connect to a non-paired device.")
                }
            }
        }

    private var currentServerSocket: BluetoothServerSocket? = null
    private var currentClientSocket: BluetoothSocket? = null

    init {
        updatePairedDevices()
        context.registerReceiver(
            bluetoothStateReceiver,
            IntentFilter().apply {
                addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED)
                addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
                addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
            },
        )
    }

    override fun startDiscovery() {
        _isScanning.value = true
        _scannedDevices.value = emptyList()
        if (!hasPermission(Manifest.permission.BLUETOOTH_SCAN) &&
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
        ) {
            return
        }

        context.registerReceiver(
            foundDeviceReceiver,
            IntentFilter(BluetoothDevice.ACTION_FOUND),
        )

        updatePairedDevices()

        bluetoothAdapter?.startDiscovery()
    }

    override fun stopDiscovery() {
        _isScanning.value = false
        if (!hasPermission(Manifest.permission.BLUETOOTH_SCAN) &&
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
        ) {
            return
        }

        bluetoothAdapter?.cancelDiscovery()
    }

    override fun connectToDevice(device: BluetoothDeviceDomain): Flow<ConnectionResult> {
        _isConnecting.value = true
        return flow {
            if (!hasPermission(Manifest.permission.BLUETOOTH_CONNECT) &&
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
            ) {
                throw SecurityException("No BLUETOOTH_CONNECT permission")
            }

            currentClientSocket =
                bluetoothAdapter
                    ?.getRemoteDevice(device.address)
                    // ?.createRfcommSocketToServiceRecord(
                    ?.createInsecureRfcommSocketToServiceRecord(
                        UUID.fromString(SERVICE_UUID),
                    )
            stopDiscovery()

            currentClientSocket?.let { socket ->

                try {
                    // 12 PAIRED 10 NOT PAIRED 11 IS PAIRING
                    if (socket.remoteDevice.bondState == 12) {
                        // socket.connect()
                    } else {
                        socket.remoteDevice.createBond()
                    }

                    if (socket.remoteDevice.bondState == 12) {
                        emit(ConnectionResult.ConnectionEstablished)
                    }
                } catch (e: IOException) {
                    socket.close()
                    _isConnecting.value = false
                    currentClientSocket = null
                    emit(ConnectionResult.Error("Connection was interrupted"))
                }
            }
        }.onCompletion {
            Log.d("rrtfdc", "onCompletion closeConnection")
            closeConnection()
            _isConnecting.value = false
        }.flowOn(Dispatchers.IO)
    }

    override fun closeConnection() {
        Log.d("rrtfdc", "closeConnection")
        currentClientSocket?.close()
        currentServerSocket?.close()
        currentClientSocket = null
        currentServerSocket = null
    }

    override fun release() {
        // TODO context.unregisterReceiver(foundDeviceReceiver) CAUSE A CRUSH WHEN CLICK NOTIF APP OPENED
      //  context.unregisterReceiver(foundDeviceReceiver)
      //  context.unregisterReceiver(bluetoothStateReceiver)
        closeConnection()
    }

    private fun updatePairedDevices() {
        if (!hasPermission(Manifest.permission.BLUETOOTH_CONNECT) &&
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
        ) {
            return
        }
        Log.d("rrtfdc", "updatePairedDevices")

        bluetoothAdapter
            ?.bondedDevices
            ?.map { it.toBluetoothDeviceDomain() }
            ?.also { devices ->
                _pairedDevices.update { devices }
            }
    }

    private fun hasPermission(permission: String): Boolean {
        return context.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
    }

    companion object {
        const val SERVICE_UUID = "00001101-0000-1000-8000-00805F9B34FB"
    }
}
