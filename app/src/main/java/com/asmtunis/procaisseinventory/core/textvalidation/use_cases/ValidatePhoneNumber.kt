package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.countrycodepicker.data.utils.checkPhoneNumber
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult

class ValidatePhoneNumber {

    fun execute(phonenumber: String, phoneCode: String, language: String): ValidationResult {
        val fullPhoneNumber = "$phoneCode$phonenumber"
        val checkPhoneNumber = checkPhoneNumber(
            phone = phonenumber,
            fullPhoneNumber = fullPhoneNumber,
            countryCode = language
        )

        if (!checkPhoneNumber) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.invalid_phone_number)
            )
        }

        return ValidationResult(
            successful = true
        )
    }
}
