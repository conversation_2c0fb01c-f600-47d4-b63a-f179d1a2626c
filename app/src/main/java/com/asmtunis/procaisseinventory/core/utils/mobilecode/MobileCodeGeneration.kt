package com.asmtunis.procaisseinventory.core.utils.mobilecode

import com.asmtunis.procaisseinventory.BuildConfig
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.simapps.ui_kit.utils.getCurrentDateInMillis

/**
 * <NAME_EMAIL>
 */
object MobileCodeGeneration {
    var versionCode =  BuildConfig.VERSION_NAME.replace(" ","")//AppUtils.getAppVersionCode().toString() // BuildConfig.VERSION_NAME;

    fun generateCommonCode(num: String, numLign: String): String {

        return versionCode + "_" + Hashids(DEVICE_ID).encode(
            getCurrentDateInMillis() + num.toLong() + numLign.toLong()
        )
    }
}
