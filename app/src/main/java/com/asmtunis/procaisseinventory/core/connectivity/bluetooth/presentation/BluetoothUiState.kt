package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation

import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothDevice

data class BluetoothUiState(
    val scannedDevices: List<BluetoothDevice> = emptyList(),
    val pairedDevices: List<BluetoothDevice> = emptyList(),
    val isConnected: Boolean = false,
    val isConnecting: <PERSON>olean = false,
    val isScanning: Boolean = false,
    val errorMessage: String? = null,
)
