package com.asmtunis.procaisseinventory.core.local_storage.localdb.core


class ProInventoryConstants {


    companion object {
        //Room
        const val PRO_INVENTORY_DB_NAME = "pro_inventory"


        const val STATION_TABLE = "Station"
        const val STATION_ARTICLE_TABLE = "Station_Article"


        const val TYPE_PRIX_UNITAIRE_HT_TABLE = "Type_Prix_Unitaire_HT"
        const val TVA_TABLE = "Tva"
        const val FAMILLE_TABLE = "Famille"
        const val MARQUE_TABLE = "Marque"
        const val FOURNISSEUR_TABLE = "Fournisseur"


        const val UNITE_ARTICLE_TABLE = "Unite_Article"
        const val UNITE_TABLE = "Unite"


        const val BON_ENTREE_TABLE = "Bon_Entree"
        const val LIGNE_BON_ENTREE_TABLE = "Ligne_Bon_Entree"

        const val BON_LIVRAISON_TABLE = "Bon_Livraison"
        const val LIGNE_BON_LIVRAISON_TABLE = "Ligne_Bon_Livraison"

        const val INVENTAIRE_TABLE = "Inventaire"
        const val LIGNE_INVENTAIRE_TABLE = "Ligne_Inventaire"



        const val TICKET_RAYON_TABLE = "Ticket_Rayon"



    }
}