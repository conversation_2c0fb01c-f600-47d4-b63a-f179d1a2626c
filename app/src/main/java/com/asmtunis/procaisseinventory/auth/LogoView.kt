package com.asmtunis.procaisseinventory.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import coil3.compose.SubcomposeAsyncImage
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState

@Composable
fun LogoView(
    logo: Any?,
    toaster: ToasterState,
) {
    val context = LocalContext.current
    val androidId = remember { DEVICE_ID }
    val clipboardManager = LocalClipboardManager.current
    SubcomposeAsyncImage(
        model = logo,
        contentDescription = "logo",
        modifier = Modifier
            .padding(18.dp)
            .background(MaterialTheme.colorScheme.primaryContainer, CutCornerShape(6.dp))
            .padding(18.dp)
            .height(100.dp)
            .fillMaxWidth(0.8f),
        loading = { LottieAnim(lotti = R.raw.loading, size = 200.dp) },
        error =  {
            Image(
                painter = painterResource(id = R.drawable.ic_asm),
                contentDescription = "",
            )

        }

    )



    Text(
        modifier = Modifier.fillMaxWidth().wrapContentWidth(Alignment.CenterHorizontally)
            .clickable {

                showToast(
                    context = context,
                    toaster = toaster,
                    message = AnnotatedString(androidId).text + "\n"+ context.resources.getString(R.string.Copied),
                    type =  ToastType.Success,
                )
                clipboardManager.setText(AnnotatedString(androidId))
            },
        text = AnnotatedString(androidId),
        fontSize = MaterialTheme.typography.bodySmall.fontSize
    )
}