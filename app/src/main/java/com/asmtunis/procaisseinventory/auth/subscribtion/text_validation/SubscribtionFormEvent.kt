package com.asmtunis.procaisseinventory.auth.subscribtion.text_validation

import com.asmtunis.countrycodepicker.data.CountryData

sealed class SubscribtionFormEvent {
    data class NomsocieteChanged(val nomsociete: String) : SubscribtionFormEvent()
    data class EtablissementChanged(val etablissement: String) : SubscribtionFormEvent()
    data class EmailChanged(val email: String) : SubscribtionFormEvent()

    //   data class PasswordChanged(val password: String) : SubscribtionFormEvent()
    data class PhoneNumberChanged(val phonenumber1: String) : SubscribtionFormEvent()
    data class CountryDataChanged(val countryData: CountryData) : SubscribtionFormEvent()
    data class SubscriptionTimeChanged(val subscriptionTime: String) : SubscribtionFormEvent()
    data class ProductSelectedChanged(val productSelected: Boolean) : SubscribtionFormEvent()

    object SubmitSubscribtion : SubscribtionFormEvent()
}
