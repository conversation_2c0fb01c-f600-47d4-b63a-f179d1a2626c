package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DemandeHistory(
    @SerialName("activat")
    val activat: String,
    @SerialName("date_recep")
    val dateRecep: String,
    @SerialName("datef")
    val datef: String,
    @SerialName("demo")
    val demo: String,
    @SerialName("device")
    val device: String,
    @SerialName("email")
    val email: String,
    @SerialName("etablissement")
    val etablissement: String,
    @SerialName("heure_recep")
    val heureRecep: String,
    @SerialName("id")
    val id: Int,
    @SerialName("id_CPU")
    val idCPU: String?,
    @SerialName("id_device")
    val idDevice: String,
    @SerialName("id_integrateur")
    val idIntegrateur: String,
    @SerialName("jours")
    val jours: String,
    @SerialName("nom_prenom")
    val nomPrenom: String,
    @SerialName("offre")
    val offre: String?,
    @SerialName("pays")
    val pays: String,
    @SerialName("produit")
    val produit: String,
    @SerialName("telephone")
    val telephone: String,
    @SerialName("type")
    val type: String,
    @SerialName("version")
    val version: String
)
