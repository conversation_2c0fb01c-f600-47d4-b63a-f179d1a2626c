package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentDetailRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn

@Composable
fun InvBatimentList(
    articleMap: Map<String, Article>,
    imageList: List<ImagePieceJoint>,
    invPatrimoineViewModel: InventaireViewModel,
    selectPatrimoineVM: SelectPatrimoineViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    navigate: (route: Any) -> Unit,
    selectedBaseconfig: BaseConfig,
    searchTextState: TextFieldValue,
    setCodM: (String)-> Unit,
    immobilisationList: List<Immobilisation>,
    isConnected: Boolean,
    isRefreshing: Boolean,
    getImmobilisation: (BaseConfig) -> Unit,
    typePat: String = "",
    marqueList: List<Marque>,
    invPatrimoineList: Map<BonCommande, List<LigneBonCommande>>,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
    getClientName: (cltName: String?, cltCode: String?) -> String,
) {

    val listState = rememberLazyListState()

    if (invPatList.isNotEmpty()) {
        val listBonCommande: MutableList<BonCommande> = arrayListOf()
        val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()

        invPatList.forEach { (key, value) ->
            run {
                listBonCommande.add(key)
                ligneBonCommande.addAll(value)
            }
        }



        PullToRefreshLazyColumn(
            items = listBonCommande,
            lazyListState = listState,
            isRefreshing = isRefreshing,
            pullToRefreshEnabled = !listBonCommande.any { !it.isSync } && isConnected,
            onRefresh = { getImmobilisation(selectedBaseconfig) },
            key = { bonCommand ->
                bonCommand.dEVNum
            },



            content = { bonCommand ->
                val enInstance = if (typePat == TypePat.DEP_OUT.typePat) {
                    if (bonCommand.dEVEtatBon == "1") " (${stringResource(id = R.string.en_instance)})" else ""
                } else ""
                ListItem(
                    onItemClick = {
                        goToInventaireBatimentScreen(
                        navigate = { navigate(it) },
                        setCodM = { setCodM(it) },
                        marqueList = marqueList,
                        articleMap = articleMap,
                        ligneBonCommande = ligneBonCommande,
                        immobilisationList = immobilisationList,
                        bonCommand = bonCommand,
                        invPatList = invPatList,
                        imageList = imageList,
                        selectPatrimoineVM = selectPatrimoineVM,
                        batimentViewModel = batimentViewModel,
                        invPatrimoineViewModel = invPatrimoineViewModel,
                        )

                    },
                    firstText = bonCommand.dEVNum,
                    firstTextRemarque = enInstance,
                    secondText = getClientName(bonCommand.dEVClientName, bonCommand.dEVCodeClient),
                    thirdText = stringPlural(
                        nbr = invPatList[bonCommand]?.size ?: 0,
                        single = stringResource(id = R.string.article_title),
                        plural = stringResource(id = R.string.article_field_title)
                    ),
                    //  colorInfo = if(typePat == TypePat.DEP_OUT.typePat) {if(bonCommande[index].dEVEtatBon== "1") MaterialTheme.colorScheme.error else LocalContentColor.current} else LocalContentColor.current,
                    //forthText = if(typePat == TypePat.DEP_OUT.typePat && bonCommande[index].dEVEtatBon== "1") "En Instance" else "",
                    dateText = bonCommand.dEVDDmFormatted?: "N/A",
                    isSync = bonCommand.isSync,
                    status = bonCommand.status,
                    onResetDeletedClick = {
                        // TODO
                    },
                    moreClickIsVisible = !bonCommand.isSync,
                    onMoreClick = {
                        invPatrimoineViewModel.restInvPatrimoine()
                        invPatrimoineViewModel.onSelectedInvPatrimoineChange(invPatList.filter { it.key == bonCommand })
                        invPatrimoineViewModel.onShowCustomModalBottomSheetChange(true)

                    },
                )
            },
        )


    } else {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

            if (searchTextState.text.isBlank() && invPatrimoineList.isEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                OutlinedButton(onClick = { getImmobilisation(selectedBaseconfig) }) {
                    Text(text = stringResource(id = R.string.get_data))
                }
            }
        }

    }
}


fun goToInventaireBatimentScreen(
    navigate: (route: Any) -> Unit,
    setCodM: (String)-> Unit,
    marqueList: List<Marque>,
    articleMap: Map<String, Article>,
    ligneBonCommande: List<LigneBonCommande>,
    immobilisationList: List<Immobilisation>,
    bonCommand: BonCommande,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
    imageList: List<ImagePieceJoint>,
    selectPatrimoineVM: SelectPatrimoineViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    invPatrimoineViewModel: InventaireViewModel,
    autoOpenDialog: Boolean = false,
    preFilledNumSerie: String = ""
) {
    invPatrimoineViewModel.restInvPatrimoine()
    val bonCommandeWithLignes = invPatList.filter { it.key == bonCommand }
    invPatrimoineViewModel.onSelectedInvPatrimoineChange(bonCommandeWithLignes)

    if (bonCommand.status == ItemStatus.WAITING.status) {
        setCodM(bonCommand.devCodeM)
        val immobilsation =
            immobilisationList.firstOrNull { it.cLICode == bonCommand.dEVCodeClient }
                ?: Immobilisation(
                    cLICode = bonCommand.dEVCodeClient ?: "N/A",
                    cLINomPren = bonCommand.dEVClient ?: "N/A"
                )
        batimentViewModel.onSelectedZoneConsomationChange(immobilsation, "5")
        selectPatrimoineVM.resetSelectedPatrimoineArticles()

        val listLigneBonCommande = bonCommandeWithLignes.values.flatten()

        for (i in listLigneBonCommande.indices) {
            val article = articleMap[ligneBonCommande[i].lGDEVCodeArt]?: Article(aRTCodeBar = ligneBonCommande[i].lGDEVCodeArt)
            val imgList = imageList.filter { it.vcNumSerie ==  ligneBonCommande[i].lGDevNumSerie }

            selectPatrimoineVM.setConsultationSelectedPatrimoineList(
                article = article,
                numSerie = listLigneBonCommande[i].lGDevNumSerie?: "N/A",
                quantity = StringUtils.stringToDouble(listLigneBonCommande[i].lGDEVQte),
                imageList = imgList,
                note = listLigneBonCommande[i].lgDEVNote?: "",
                marque = marqueList.firstOrNull { it.mARCode == listLigneBonCommande[i].lGDEVCMarq }?: Marque(mARDesignation = listLigneBonCommande[i].lGDEVCMarq ?: "N/A")
            )
        }


        navigate(AjoutInventaireBatimentRoute(autoOpenDialog = autoOpenDialog, preFilledNumSerie = preFilledNumSerie))
    } else navigate(InventaireBatimentDetailRoute)
}