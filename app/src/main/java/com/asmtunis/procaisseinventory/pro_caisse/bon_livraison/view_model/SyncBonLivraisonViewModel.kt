package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.TICKET_FACTURE_DEJA
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncBonLivraisonViewModel
@Inject
constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher val mainDispatcher: CoroutineDispatcher,
    val proCaisseRemote: ProCaisseRemote,
    val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    private val dataStoreRepository: DataStoreRepository,
    // app: Application
) : ViewModel() {


    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private var listActifTimberFlow = proCaisseLocalDb.timbre.getActif().distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    var responseAddBatchTicketWithLignesState: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set
    var notSyncAddBatchTicketWithLignesObj : String by mutableStateOf("")
        private set
    var responseFactureTicket: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set

    var ticketsWithLinesAndPaymentsNotSync: List<TicketWithFactureAndPayments> by mutableStateOf(emptyList())
        private set
    init {
        getNotSyncBonLivraison()
    }


    var listActifTimber by  mutableStateOf(emptyList<Timbre>())
        private set


    private fun getNotSyncBonLivraison() {
        viewModelScope.launch {
            val bonLivraisonNotSyncFlow = proCaisseLocalDb.bonLivraison.notSynced().distinctUntilChanged()


            combine(networkFlow, bonLivraisonNotSyncFlow, autoSyncFlow, listActifTimberFlow) { isConnected, inventaireNotSyncList, autoSync, listActifTimberFlow ->
                listActifTimber = listActifTimberFlow.ifEmpty { emptyList() }
                connected = isConnected
                autoSyncState = autoSync
                inventaireNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    ticketsWithLinesAndPaymentsNotSync = emptyList()
                    return@collect
                }
                ticketsWithLinesAndPaymentsNotSync = it
                if(connected && autoSyncState) syncBonLivraison()
            }
        }
    }


    private fun setFactureTimbreAndRevImp() {

        val timberValue = timbersValueSum(listActifTimber)

        ticketsWithLinesAndPaymentsNotSync.forEach {ticketsWithLinesAndPayments->
            val client = ticketsWithLinesAndPayments.client
            val ticket = ticketsWithLinesAndPayments.ticket

            ticketsWithLinesAndPayments.ticket = ticket?.copy(
                tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0" else listActifTimber.first().tIMBCode
            )
        }

    }
    fun syncBonLivraison(selectedTicket: TicketWithFactureAndPayments = TicketWithFactureAndPayments()) {
        viewModelScope.launch(dispatcherIO) {
            val autoFacture = proCaisseLocalDb.dataStore.getBoolean(AUTO_FACTURE_AUTHORISATION).first()

            if(autoFacture) {
                setFactureTimbreAndRevImp()
            }

            // Récupérer l'ID de la session de caisse active
            val activeSessionId = getActiveSessionId()
            Log.d("SyncBonLivraisonViewModel", "Synchronisation avec la session de caisse active: $activeSessionId")

            // Ajouter l'ID de session aux tickets à synchroniser
            if (!activeSessionId.isNullOrEmpty()) {
                if (selectedTicket != TicketWithFactureAndPayments()) {
                    // Mettre à jour le ticket sélectionné
                    selectedTicket.ticket?.let { ticket ->
                        val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId.toString())
                        selectedTicket.ticket = updatedTicket
                    }
                } else {
                    // Mettre à jour tous les tickets non synchronisés
                    ticketsWithLinesAndPaymentsNotSync.forEach { ticketWithLines ->
                        ticketWithLines.ticket?.let { ticket ->
                            val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId.toString())
                            ticketWithLines.ticket = updatedTicket
                        }
                    }
                }
            }

            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(if(selectedTicket != TicketWithFactureAndPayments()) listOf(selectedTicket)  else ticketsWithLinesAndPaymentsNotSync),
                )


            notSyncAddBatchTicketWithLignesObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.ticket.addBatchTicketWithLignesTicketAndPayment(
                baseConfig = notSyncAddBatchTicketWithLignesObj,
                autoFacture = autoFacture,
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = result.data, loading = false, error = null)

                        for (i in result.data!!.indices) {

                            val ticketUpdate = result.data[i]
                            Log.d("SyncBonLivraisonViewModel", "Code de réponse: ${ticketUpdate.code}, Message: ${ticketUpdate.message}")

                            // Gérer les différents codes de réponse
                            when (ticketUpdate.code) {
                                "10200", "10201" -> {
                                    // Succès ou mise à jour réussie
                                    updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture,autofacture)
                                }
                                "10302" -> {
                                    // Ticket n'existe pas - essayer de le créer
                                    Log.d("SyncBonLivraisonViewModel", "Ticket n'existe pas, tentative de création")
                                    // Utiliser updateLocalData pour gérer ce cas
                                    updateLocalData(
                                        ticketUpdate = ticketUpdate,
                                        insertFacture = autoFacture,
                                        autofacture = autofacture
                                    )
                                }
                                else -> {
                                    // Autres erreurs
                                    responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = ticketUpdate.message)

                                    // Mettre à jour le message d'erreur dans la base de données locale
                                    ticketUpdate.tIKNumTicketM?.let { tikNumTicketM ->
                                        proCaisseLocalDb.bonLivraison.updateSyncErrorMsg(
                                            tikNumTicketM = tikNumTicketM,
                                            errorMsg = ticketUpdate.message ?: "UnkownError",
                                        )
                                    }
                                }
                            }
                        }
                    }

                    is DataResult.Loading -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedTicket.ticket?.tIKNumTicket)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
    fun facturerBL(ticketWithFactureAndPayments: TicketWithFactureAndPayments) {

        ticketsWithLinesAndPaymentsNotSync = emptyList()
        ticketsWithLinesAndPaymentsNotSync = listOf(ticketWithFactureAndPayments)
        setFactureTimbreAndRevImp()

        viewModelScope.launch(dispatcherIO) {
            // Check network connectivity before attempting synchronization
            if (!connected) {
                responseFactureTicket = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "No network connection available. Please check your internet connection and try again."
                )
                return@launch
            }
            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(ticketsWithLinesAndPaymentsNotSync),
                )

            proCaisseRemote.ticket.addBatchFactureWithLines(baseConfig = Json.encodeToString(baseConfigObj)).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseFactureTicket = RemoteResponseState(data = result.data, loading = false, error = null)
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        for (i in result.data!!.indices) {
                            val ticketUpdate = result.data[i]
                            if (!ticketUpdate.code.equals("10200") && !ticketUpdate.code.equals("10201") && !ticketUpdate.code.equals("10304")) {
                                // TODO HANDLE OTHER CASES HERE
                                return@onEach
                            }

                            updateLocalData(ticketUpdate = ticketUpdate, insertFacture = true, autofacture = true )
                        }
                    }

                    is DataResult.Loading -> {
                        responseFactureTicket = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        responseFactureTicket = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    private fun updateClientSold(
        cLICode: String,
        ticketUpdate: TicketUpdate,
    )  {
        viewModelScope.launch(dispatcherIO) {
            proCaisseLocalDb.clients.updateMoneyClient(
                codeClt = cLICode,
                soldClient = ticketUpdate.soldeClient?: "",
                cliCredit = ticketUpdate.credit?: "",
                cliDebit = ticketUpdate.debit?: "",
            )
        }
    }

    private fun updateLocalData(
        ticketUpdate: TicketUpdate,
        insertFacture: Boolean,
        autofacture: autofacture
    ) {
        val ddm = getCurrentDateTime()

        if(insertFacture) {
            ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
        }
        proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
            codeM = ticketUpdate.tIKNumTicketM?: "",
            newCode = ticketUpdate.tIKNumTicket,
            exercice = ticketUpdate.tIKExerc?: "",
            carnet = ticketUpdate.tIKIdCarnet?: "",
        )
        if (ticketUpdate.tIKNumeroBL == null) {
            // Auto facture == true

            proCaisseLocalDb.bonLivraison.updateBLNumber(
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM?: "",
                tikDdm = ddm,
            )
        } else {
            // Auto facture == false
            proCaisseLocalDb.bonLivraison.updateTicketNumber(
                tikNumBl = ticketUpdate.tIKNumeroBL?: "",
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM!!,
                tikDdm = ddm,
            )
        }

        if (ticketUpdate.observation != null) {
            if (!ticketUpdate.observation.equals("")) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.observation?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        if (ticketUpdate.message != null) {
            if (ticketUpdate.message.equals(TICKET_FACTURE_DEJA)) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.message?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        updateClientSold(
            cLICode = ticketUpdate.codeClient?: "",
            ticketUpdate = ticketUpdate,
        )

        // TODO ONLE EXC WHEN IS NOT CREDIT
        proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.ticketResto.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )
    }

    suspend fun getActiveSessionId(): String {
        // Récupérer la session de caisse active depuis la base de données locale
        return try {
            Log.d("SyncBonLivraisonViewModel", "Récupération de toutes les sessions...")
            val sessions = proCaisseLocalDb.sessionCaisse.getAll().first()
            Log.d("SyncBonLivraisonViewModel", "Nombre de sessions trouvées: ${sessions.size}")

            val activeSession = sessions.firstOrNull { it.sCClotCaisse == 0 } // Session non clôturée
            Log.d("SyncBonLivraisonViewModel", "Session active trouvée: ${activeSession?.sCIdSCaisse ?: "Aucune"}")

            activeSession?.sCIdSCaisse ?: ""
        } catch (e: Exception) {
            Log.e("SyncBonLivraisonViewModel", "Erreur lors de la récupération de la session active: ${e.message}", e)
            ""
        }
    }

    private suspend fun updateTicketWithSessionId(ticketUpdate: TicketUpdate, insertFacture: Boolean, createIfNotExists: Boolean, sessionId: String) {
        Log.d("SyncBonLivraisonViewModel", "updateTicketWithSessionId démarré - createIfNotExists=$createIfNotExists, sessionId=$sessionId")
        val ddm = getCurrentDateTime()

        // Insérer la facture si nécessaire
        if(insertFacture) {
            Log.d("SyncBonLivraisonViewModel", "Insertion de la facture...")
            ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
        }

        // Mettre à jour les lignes de ticket avec le nouveau numéro de ticket
        proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
            codeM = ticketUpdate.tIKNumTicketM?: "",
            newCode = ticketUpdate.tIKNumTicket,
            exercice = ticketUpdate.tIKExerc?: "",
            carnet = ticketUpdate.tIKIdCarnet?: "",
        )

        // Si createIfNotExists est true (code 10302), créer un nouveau ticket
        if (createIfNotExists) {
            Log.d("SyncBonLivraisonViewModel", "Création d'un nouveau ticket pour le journal de caisse")

            // Créer un nouveau ticket basé sur les données de la réponse
            val newTicket = Ticket(
                tIKNumTicket = ticketUpdate.tIKNumTicket.toString(),
                tIKNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                tIKExerc = ticketUpdate.tIKExerc ?: "",
                tIKIdCarnet = ticketUpdate.tIKIdCarnet ?: "",
                tIKCodClt = ticketUpdate.codeClient ?: "",
                tIKIdSCaisse = sessionId // Associer à la session de caisse active
            )

            // Mettre à jour les propriétés de BaseModel
            newTicket.isSync = true
            newTicket.status = "JOURNAL_CAISSE" // Important: utiliser JOURNAL_CAISSE pour qu'il apparaisse dans le journal

            // Insérer le nouveau ticket dans la base de données locale
            proCaisseLocalDb.bonLivraison.upsert(newTicket)

            Log.d("SyncBonLivraisonViewModel", "Nouveau ticket créé avec succès: ${newTicket.tIKNumTicketM}")

            // Mettre à jour les informations du ticket
            if (ticketUpdate.tIKNumeroBL == null) {
                // Auto facture == true
                proCaisseLocalDb.bonLivraison.updateBLNumber(
                    tikNumTicket = ticketUpdate.tIKNumTicket,
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    tikDdm = ddm,
                )
            } else {
                // Auto facture == false
                proCaisseLocalDb.bonLivraison.updateTicketNumber(
                    tikNumBl = ticketUpdate.tIKNumeroBL ?: "",
                    tikNumTicket = ticketUpdate.tIKNumTicket,
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    tikDdm = ddm,
                )
            }

            // Mettre à jour l'ID de session de caisse pour ce ticket
            Log.d("SyncBonLivraisonViewModel", "Association du ticket ${ticketUpdate.tIKNumTicketM} avec la session $sessionId")
            proCaisseLocalDb.bonLivraison.updateSessionId(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                sessionId = sessionId
            )

            // Mettre à jour le statut pour qu'il apparaisse dans le journal de caisse
            Log.d("SyncBonLivraisonViewModel", "Mise à jour du statut du ticket ${ticketUpdate.tIKNumTicketM} vers JOURNAL_CAISSE")
            proCaisseLocalDb.bonLivraison.updateTicketStatus(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                status = "JOURNAL_CAISSE"
            )
        } else {
            // Mise à jour normale du ticket existant
            if (ticketUpdate.tIKNumeroBL == null) {
                // Auto facture == true
                proCaisseLocalDb.bonLivraison.updateBLNumber(
                    tikNumTicket = ticketUpdate.tIKNumTicket,
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    tikDdm = ddm,
                )
            } else {
                // Auto facture == false
                proCaisseLocalDb.bonLivraison.updateTicketNumber(
                    tikNumBl = ticketUpdate.tIKNumeroBL ?: "",
                    tikNumTicket = ticketUpdate.tIKNumTicket,
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    tikDdm = ddm,
                )
            }

            // Si on a une session active, mettre à jour l'ID de session pour ce ticket
            if (sessionId.isNotEmpty()) {
                Log.d("SyncBonLivraisonViewModel", "Association du ticket existant ${ticketUpdate.tIKNumTicketM} avec la session $sessionId")
                proCaisseLocalDb.bonLivraison.updateSessionId(
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    sessionId = sessionId
                )

                // Mettre à jour le statut pour qu'il apparaisse dans le journal de caisse
                Log.d("SyncBonLivraisonViewModel", "Mise à jour du statut du ticket existant ${ticketUpdate.tIKNumTicketM} vers JOURNAL_CAISSE")
                proCaisseLocalDb.bonLivraison.updateTicketStatus(
                    tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                    status = "JOURNAL_CAISSE"
                )
            }
        }

        // Mettre à jour les observations si nécessaire
        ticketUpdate.observation?.let { observation ->
            if (observation.isNotEmpty()) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = observation,
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        // Gérer le message de ticket déjà facturé
        ticketUpdate.message?.let { message ->
            if (message == TICKET_FACTURE_DEJA) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = message,
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        // Mettre à jour le solde client
        updateClientSold(
            cLICode = ticketUpdate.codeClient?: "",
            ticketUpdate = ticketUpdate,
        )

        // Mettre à jour les règlements associés au ticket
        proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        // Mettre à jour les chèques associés au ticket
        proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        // Mettre à jour les tickets resto associés au ticket
        proCaisseLocalDb.ticketResto.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        Log.d("SyncBonLivraisonViewModel", "Synchronisation des tickets terminée")
    }

}
