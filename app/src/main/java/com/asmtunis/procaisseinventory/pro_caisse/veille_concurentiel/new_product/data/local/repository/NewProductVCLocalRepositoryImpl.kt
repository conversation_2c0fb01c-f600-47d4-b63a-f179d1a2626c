package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.dao.NewProductVCDAO
import kotlinx.coroutines.flow.Flow


class NewProductVCLocalRepositoryImpl(
    private val newProductVCDAO: NewProductVCDAO
) : NewProductVCLocalRepository {


    override fun upsertAll(value: List<NewProductVC>) = newProductVCDAO.insertAll(value)
    override fun upsert(value: NewProductVC)= newProductVCDAO.insert(value)


    override fun deleteAll() = newProductVCDAO.deleteAll()
    override fun delete(value: NewProductVC) = newProductVCDAO.delete(newProductVC = value)
    override fun deleteByCode(code : String) =
        newProductVCDAO.deleteByCode(code = code)

    override fun setDeleted(code: String, codeMobile: String) =
        newProductVCDAO.setDeleted(
        code = code,
        codeMobile = codeMobile
    )

    override fun getNoSyncedToDelete(): Flow<List<NewProductVC>> = newProductVCDAO.getNoSyncedToDelete

    override fun getAll(): Flow<List<NewProductVC>> = newProductVCDAO.all
    override fun noSyncedToAddOrUpdate(): Flow<List<NewProductVC>?>  = newProductVCDAO.noSyncedToAddOrUpdate
    override fun updateCloudCode(code: String, codeMobile: String)
    = newProductVCDAO.updateCloudCode(code = code, codeMobile = codeMobile)

    override fun restDeleted(code: String,status : String, isSync:Boolean)
            = newProductVCDAO.restDeleted(code = code,status =status, isSync =isSync)


    override fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>> = newProductVCDAO.filterByNum(
        searchString = searchString,
        sortBy = sortBy,
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        isAsc = isAsc
    )

    override fun filterByPrixProduct(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>> = newProductVCDAO.filterByPrixProduct(
        searchString = searchString,
        sortBy = sortBy,
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        isAsc = isAsc
    )

    override fun filterByNewProduct(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>> = newProductVCDAO.filterByNewProduct(
        searchString = searchString,
        sortBy = sortBy,
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        isAsc = isAsc
    )

    override fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String
    ): Flow<List<NewProductVCWithImages>> =
        newProductVCDAO.getAllFiltred(
            isAsc = isAsc,
            filterByTypComm = filterByTypComm,
            filterByConcurrent = filterByConcurrent,
            sortBy = sortBy
        )
}
