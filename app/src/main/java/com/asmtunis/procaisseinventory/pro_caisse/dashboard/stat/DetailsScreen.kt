package com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.data.PieChartSliceData
import com.asmtunis.procaisseinventory.shared_ui_components.PieChart


@Composable
fun StatementBody(
    circleLabel: String,
    circleLableTotal: Double = 0.0,
    priceFormatTotalSum: Boolean,
    items: List<PieChartSliceData>,
    rows: @Composable (PieChartSliceData) -> Unit
) {
        Spacer(Modifier.height(50.dp))
        Box(Modifier.padding(16.dp)) {
         //   val accountsProportion = items.extractProportions { it.amount.toFloat() }
          //  val circleColors = items.map { it.color }
            Spacer(Modifier.height(30.dp))

          /*  AnimatedCircle(
                proportions = accountsProportion,
                colors = circleColors,
                modifier = Modifier
                    .size(300.dp)
                    .align(Alignment.Center)
                   // .fillMaxWidth()
            )*/
            PieChart(
                priceFormatTotalSum = priceFormatTotalSum,
                sliceData = items,
                regTotalMnt = circleLableTotal,
                circleLabel = circleLabel
            )
          /*  Column(modifier = Modifier.align(Alignment.Center)) {
                Text(
                    text = amountsTotal,
                    color = MaterialTheme.colorScheme.primary,
                    // style = MaterialTheme.typography.h2,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
                Text(
                    text = circleLabel,
                    color = MaterialTheme.colorScheme.primary,
                 //   style = MaterialTheme.typography.body1,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )

            }*/
        }
        Spacer(Modifier.height(10.dp))

        items.forEach { item ->
                    OutlinedCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        shape = RoundedCornerShape(10.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                    rows(item)
                }
            }



 //   }
}