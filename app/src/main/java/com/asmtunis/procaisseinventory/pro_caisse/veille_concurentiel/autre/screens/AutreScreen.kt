package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.navigation.AutreDetailRoute
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.AutreViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel


@Composable
fun AutreScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    listState: LazyListState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    cameraViewModel: CameraViewModel,
    mainViewModel: MainViewModel,
    vcViewModel: VeilleConcurentielViewModel,
    autreViewModel: AutreViewModel,
    textValidationViewModel: VcTextValidationViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val autreState = autreViewModel.autreListstate

    LaunchedEffect(key1 = autreViewModel.autreSearchTextState.text, key2 = autreState.lists, key3 = autreState.search) {
        autreViewModel.filterAutreVC(autreState)
    }


    val utilisateur = mainViewModel.utilisateur

    val concurentList = mainViewModel.listConcurentVC
    val autreList = autreViewModel.autreListstate.lists










    if(autreList.isNotEmpty())
    ListAutre (
        navigate = { navigate(it) },
        popBackStack = { popBackStack() },
        isConnected = isConnected,
        selectedBaseconfig = selectedBaseconfig,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        listState = listState,
        cameraViewModel = cameraViewModel,
        textValidationViewModel = textValidationViewModel,
        autreList = autreList,
    vcViewModel = vcViewModel,
    autreViewModel = autreViewModel,
        concurentList = concurentList,
        mainViewModel = mainViewModel,
        utilisateur = utilisateur
    )
    else Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
    }
}

@Composable
fun ListAutre (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    mainViewModel: MainViewModel,
    concurentList: List<ConcurrentVC>,
    listState: LazyListState,
    cameraViewModel: CameraViewModel,
    textValidationViewModel: VcTextValidationViewModel,
    autreList: List<AutreVCWithImages>,
    vcViewModel: VeilleConcurentielViewModel,
    autreViewModel: AutreViewModel,
    utilisateur:   Utilisateur
){

    val isRefreshing  = getProCaisseDataViewModel.autreState.loading

    PullToRefreshLazyColumn(
        items = autreList,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !autreList.any { it.autreVC?.isSync != true } && isConnected,
        onRefresh = {
            getProCaisseDataViewModel.getVCAutre(
                baseConfig = selectedBaseconfig
            )
        },
        key = { autrList -> autrList.autreVC?.id?: autrList.autreVC.toString() },
        content = { autrList ->
            ListItem(
                onItemClick={
                    val prefixe = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "VCAutre" }?.pREPrefixe?: "VC_A_M"
                    val prefixImage = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "VC_Image" }?.pREPrefixe?: "VC_A_M_IMG"

                    mainViewModel.generateCodeM(
                        utilisateur = utilisateur,
                        prefix = prefixe,
                        prefixImage = prefixImage
                    )
                    cameraViewModel.addListImageUri(autrList.imageList?: emptyList())
                    autreViewModel.onselectedAutreChange(autrList)
                    autreViewModel.onModifyChange(false)
                   // cameraViewModel.addImageUri(imgUri = EMPTY_IMAGE_URI, context = context)
                    navigate(AutreDetailRoute)

                    textValidationViewModel.resetVariable()
                },
                firstText = autrList.autreVC?.codeAutre?: "N/A",
                secondText = stringResource(id = R.string.concurrent_value, concurentList.firstOrNull { it.codeconcurrent == (autrList.autreVC?.codeConcur ?: "N/A") }?.concurrent?: autrList.autreVC?.codeConcur?: "N/A"),
                thirdText =  stringResource(id = R.string.type_communication_value, vcViewModel.typeCommunicationVCList.firstOrNull { it.codeTypeCom == (autrList.autreVC?.codeTypeCom ?: "N/A") }?.typeCommunication?: autrList.autreVC?.codeTypeCom?: "N/A"),
                forthText = stringResource(id = R.string.other_value, autrList.autreVC?.autre?: "N/A"),
                dateText = autrList.autreVC?.dateOp?: "N/A",
                isSync = autrList.autreVC?.isSync == true,
                status = autrList.autreVC?.status?: "N/A",
                onResetDeletedClick = { autreViewModel.restDeletedAutre(autrList) },
                onMoreClick = {
                    autreViewModel.onselectedAutreChange(autrList)
                    vcViewModel.onShowCustomModalBottomSheetChange(true)

                }
            )
        }
    )


}
