package com.asmtunis.procaisseinventory.pro_caisse.client.screens

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.DeleteOutline
import androidx.compose.material.icons.twotone.EditNote
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.MODIFY_CLIENT
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils.isValidGPScoordinates
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils.openGoogleMapNav
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddClientRoute
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.components.ClientInfoBody
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.components.ClientInfoHeader
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.FondCaisseInputView
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

const val maxHeight = 200f
const val minHeight = 60f

@Composable
fun ClientInfoScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    mainViewModel: MainViewModel,
    settingViewModel: SettingViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    locationViewModule: LocationViewModule,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    syncSharedViewModels: SyncSharedViewModels? = null,
    syncProcaisseViewModels: SyncProcaisseViewModels? = null
) {
    val textValidationViewModel = proCaisseViewModels.clientTextValidationViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val distNumViewModel = proCaisseViewModels.distNumViewModel
    val reglementCaisseViewModel = proCaisseViewModels.reglementCaisseViewModel
    val paymentViewModel = proCaisseViewModels.paymentViewModel
    val clientViewModel = proCaisseViewModels.clientViewModel
    val bonRetourViewModel = proCaisseViewModels.bonRetourViewModel
    val bonCommandeVM = proCaisseViewModels.bonCommandeViewModel
    val bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel

    val context = LocalContext.current

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    val utilisateur = mainViewModel.utilisateur
    val prefixList = mainViewModel.prefixList
    val bonRetourList = bonRetourViewModel.bonRetourList

    val prefixeClient = prefixList.firstOrNull { it.pREIdTable == "Client" }?.pREPrefixe?: "InvD_"

    val initialCodeM =prefixeClient+"_" + utilisateur.Station + "_" // used to check with client is added from mobile or not /// if not added by mobile then cant delete it after update

    val client = mainViewModel.clientList.firstOrNull { it.cLICode == clientId }?: Client()



    val blList = clientViewModel.blList
    val invPatList = clientViewModel.invPatList
    val isConnected = networkViewModel.isConnected

    val listeReglementLibreByClient = reglementCaisseViewModel.listeReglementLibreByClient
    val sessionCaisse = navDrawerViewModel.sessionCaisse
    val clientIsSync = client.isSync
    val bcList = clientViewModel.bcList

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val canModifyClt = proCaisseAuthorization.any { it.AutoCodeAu == MODIFY_CLIENT}





    val addSessionCaisseState = mainViewModel.addSessionCaisseState

    // État pour forcer l'actualisation de l'interface après ouverture/fermeture de session
    var sessionUpdateTrigger by remember { mutableStateOf(0) }
    val selectedSessionCaisse = bonLivraisonVM.selectedSessionCaisse
    val visitesList = clientViewModel.visitesList

    // État pour suivre si une synchronisation est en cours
    var isSyncing by remember { mutableStateOf(false) }

    // États pour forcer l'affichage des boutons
    var forceShowOpenButton by remember { mutableStateOf(false) }
    var forceShowCloseButton by remember { mutableStateOf(false) }

    // Scope pour lancer des coroutines
    val scope = rememberCoroutineScope()

    LaunchedEffect(key1 = Unit) {

        selectArtMobilityVM.resetSelectedMobilityArticles()
        selectPatrimoineVM.resetSelectedPatrimoineArticles()
        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())

        if (client == Client()) {
            popBackStack()
            return@LaunchedEffect
        }

        clientViewModel.getVisitesListByClient(client.cLICode)
        clientViewModel.getBCListByClientAndStation(codeClient = client.cLICode, station =  utilisateur.Station)

        val selectedSessionCaisse = if(selectedSessionCaisse == SessionCaisse()) sessionCaisse
        else selectedSessionCaisse

        clientViewModel.getBLListByClientAndSession(codeClient = client.cLICode, sCIdSCaisse = selectedSessionCaisse.sCIdSCaisse)
        bonRetourViewModel.getBonRetourListByClient(client.cLICode)

        reglementCaisseViewModel.getReglementListByClient(codeClt = client.cLICode)

    }

    // Recharger les données quand l'état de la session change
    LaunchedEffect(key1 = sessionUpdateTrigger, key2 = sessionCaisse) {
        Log.d("ClientInfoPane", "Actualisation de l'interface après changement de session")
        // Forcer la mise à jour de la session depuis le serveur
        mainViewModel.getSessionCaisseByUser(mainViewModel.selectedBaseconfig, utilisateur.codeUt)

        // Attendre un peu plus longtemps pour que la session soit mise à jour
        delay(1500)

        // Vérifier l'état actuel de la session
        Log.d("ClientInfoPane", "Vérification de l'état de la session: sCIdSCaisse=${sessionCaisse.sCIdSCaisse}, sCClotCaisse=${sessionCaisse.sCClotCaisse}")

        // Forcer la mise à jour de l'interface
        navDrawerViewModel.refreshSessionCaisse()

        // Attendre que la mise à jour soit terminée
        delay(500)

        // Vérifier l'état de la session après la mise à jour
        val hasOpenSession = navDrawerViewModel.sessionCaisse.sCIdSCaisse.isNotEmpty() && navDrawerViewModel.sessionCaisse.sCClotCaisse == 0
        Log.d("ClientInfoPane", "Session après mise à jour: sCIdSCaisse=${navDrawerViewModel.sessionCaisse.sCIdSCaisse}, sCClotCaisse=${navDrawerViewModel.sessionCaisse.sCClotCaisse}, hasOpenSession=$hasOpenSession")

        // Réinitialiser les états de forçage une fois que la session est correctement mise à jour
        if (hasOpenSession && forceShowCloseButton) {
            // La session est maintenant ouverte, on peut arrêter de forcer l'affichage du bouton "Clôturer session"
            forceShowCloseButton = false
        } else if (!hasOpenSession && forceShowOpenButton) {
            // La session est maintenant fermée, on peut arrêter de forcer l'affichage du bouton "Ouvrir session"
            forceShowOpenButton = false
        }

        // Recharger les données du client pour afficher les fonctionnalités
        if (hasOpenSession) {
            // Session ouverte, charger toutes les données
            Log.d("ClientInfoPane", "Chargement des données du client avec session ouverte")
            clientViewModel.getVisitesListByClient(client.cLICode)
            clientViewModel.getBCListByClientAndStation(codeClient = client.cLICode, station = utilisateur.Station)
            clientViewModel.getBLListByClientAndSession(codeClient = client.cLICode, sCIdSCaisse = navDrawerViewModel.sessionCaisse.sCIdSCaisse)
            bonRetourViewModel.getBonRetourListByClient(client.cLICode)
            reglementCaisseViewModel.getReglementListByClient(codeClt = client.cLICode)
        } else {
            Log.d("ClientInfoPane", "Pas de session ouverte, chargement limité des données")
            clientViewModel.getVisitesListByClient(client.cLICode)
        }
    }

    val density = LocalDensity.current
    val d = density.density



    var progress by rememberSaveable { mutableFloatStateOf(0f) }

    val toolbarHeightPx = with(LocalDensity.current) {
        maxHeight.dp.roundToPx().toFloat()
    }

    val toolbarMinHeightPx = with(LocalDensity.current) {
        minHeight.dp.roundToPx().toFloat()
    }
    val toolbarOffsetHeightPx = rememberSaveable { mutableFloatStateOf(0f) }
    LaunchedEffect(key1 = toolbarOffsetHeightPx.floatValue) {
        progress = ((toolbarHeightPx + toolbarOffsetHeightPx.floatValue) / toolbarHeightPx - minHeight / maxHeight) / (1f - minHeight / maxHeight)
    }

    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(
                available: Offset,
                source: NestedScrollSource
            ): Offset {
                val delta = available.y
                val newOffset = toolbarOffsetHeightPx.floatValue + delta
                toolbarOffsetHeightPx.floatValue =
                    newOffset.coerceIn(toolbarMinHeightPx - toolbarHeightPx, 0f)
                return Offset.Zero
            }
        }
    }



    Scaffold(
        floatingActionButton = {

            AnimatedVisibility(
                // modifier = modifier,
                visible = progress > 0.0,
                enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                exit = slideOutVertically { with(density) { 40.dp.roundToPx() } } + fadeOut()
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (canModifyClt) {
                        FloatingActionButton(
                            onClick = {

                                val typeClientList = context.resources.getStringArray(R.array.clients_type_list)
                                textValidationViewModel.setClientVariable(client = client, typeClientList = typeClientList.toList(), utilisateur = utilisateur)

                                navigate(AddClientRoute(clientId = clientId))

                            }) {

                            Icon(
                                imageVector = Icons.TwoTone.EditNote,
                                contentDescription = stringResource(id = R.string.cd_addVisite_button)
                            )
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }

                    if(!clientIsSync && client.cLICode.contains(initialCodeM)) {
                        FloatingActionButton(
                            onClick = {
                                clientViewModel.deleteClient(client)

                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = context.resources.getString(R.string.deleted_succesfully),
                                    type =  ToastType.Success,
                                )
                                popBackStack()
                            }) {

                            Icon(
                                imageVector = Icons.TwoTone.DeleteOutline,
                                contentDescription = stringResource(id = R.string.delete)
                            )
                        }
                        Spacer(modifier = Modifier.height(12.dp))
                    }

                    if (isValidGPScoordinates(lat = client.cltLatitude, long = client.cltLongitude)) {
                        ExtendedFloatingActionButton(
                            onClick = {
                                openGoogleMapNav(
                                    context,
                                    lat = client.cltLatitude?: 0.0,
                                    longitude = client.cltLongitude?: 0.0
                                )

                            }) {
                            LottieAnim(
                                lotti = R.raw.location_pin,
                                size = 25.dp,
                                onClick = {
                                    openGoogleMapNav(
                                        context,
                                        lat = client.cltLatitude?: 0.0,
                                        longitude = client.cltLongitude?: 0.0
                                    )
                                }
                            )
                        }
                    }
                }
            }

        }
    ) { padding ->



        if(mainViewModel.showFondCaisseDialogue) {
            FondCaisseInputView(
                onDismissRequest = {
                    mainViewModel.setfondcaisse("")
                    mainViewModel.setFondCaisseDialogueVisibility(false)
                },
                onDismiss = {
                    mainViewModel.setfondcaisse("")
                    mainViewModel.setFondCaisseDialogueVisibility(false)
                },
                onConfirm = {
                    // Vérifier la connectivité avant toute opération
                    if (!isConnected) {
                        Log.e("ClientInfoPane", "Pas de connexion Internet disponible")
                        return@FondCaisseInputView
                    }

                    // Si aucune session n'est ouverte, utiliser addSessionVendeur
                    if (sessionCaisse.sCIdSCaisse.isEmpty() || sessionCaisse.sCClotCaisse == 1) {
                        Log.d("ClientInfoPane", "Ouverture d'une nouvelle session avec addSessionVendeur")
                        try {
                            // Désactiver les boutons pendant la synchronisation
                            isSyncing = true

                            // Forcer l'affichage du bouton "Clôturer session" immédiatement
                            forceShowCloseButton = true
                            forceShowOpenButton = false

                            // Ouvrir la session
                            mainViewModel.addSessionVendeur(
                                baseConfig = mainViewModel.selectedBaseconfig,
                                utilisateur = utilisateur,
                                androidId = DEVICE_ID
                            )

                            // Forcer l'actualisation de l'interface
                            sessionUpdateTrigger += 1

                            // Forcer la mise à jour de la session
                            navDrawerViewModel.refreshSessionCaisse()

                            // Recharger les données du client pour afficher les fonctionnalités
                            clientViewModel.getVisitesListByClient(client.cLICode)
                            clientViewModel.getBCListByClientAndStation(codeClient = client.cLICode, station = utilisateur.Station)
                            clientViewModel.getBLListByClientAndSession(codeClient = client.cLICode, sCIdSCaisse = sessionCaisse.sCIdSCaisse)
                            bonRetourViewModel.getBonRetourListByClient(client.cLICode)
                            reglementCaisseViewModel.getReglementListByClient(codeClt = client.cLICode)

                            // Lancer la synchronisation automatique
                            scope.launch {
                                try {
                                    // Attendre que l'ouverture soit terminée
                                    delay(1000)

                                    // Lancer la synchronisation complète
                                    Log.d("ClientInfoPane", "Début de la synchronisation après ouverture de session")
                                    mainViewModel.getSessionCaisseByUser(
                                        baseConfig = mainViewModel.selectedBaseconfig,
                                        codeUt = utilisateur.codeUt
                                    )

                                        // Attendre que la synchronisation soit terminée
                                        delay(2000)

                                        // Mettre à jour l'interface après la synchronisation
                                        Log.d("ClientInfoPane", "Mise à jour de l'interface après synchronisation")
                                        navDrawerViewModel.refreshSessionCaisse()

                                        // Vérifier l'état de la session après la mise à jour
                                        delay(500)
                                        Log.d("ClientInfoPane", "Session après mise à jour: sCIdSCaisse=${navDrawerViewModel.sessionCaisse.sCIdSCaisse}, sCClotCaisse=${navDrawerViewModel.sessionCaisse.sCClotCaisse}")

                                        // Synchronisation terminée avec succès
                                        Log.d("ClientInfoPane", "Synchronisation terminée avec succès")
                                    } catch (e: Exception) {
                                        Log.e("ClientInfoPane", "Erreur lors de la synchronisation: ${e.message}")
                                        e.printStackTrace()
                                    } finally {
                                        isSyncing = false
                                    }
                                }
                        } catch (e: Exception) {
                            Log.e("ClientInfoPane", "Erreur lors de l'ouverture de session: ${e.message}")
                            e.printStackTrace()
                            isSyncing = false
                            // Réinitialiser les états en cas d'erreur
                            forceShowCloseButton = false
                            forceShowOpenButton = false
                        }
                    } else {
                        // Sinon, utiliser closeSessionVendeur pour clôturer la session
                        Log.d("ClientInfoPane", "Clôture de la session avec closeSessionVendeur")
                        try {
                            // Désactiver les boutons pendant la synchronisation
                            isSyncing = true

                            // Forcer l'affichage du bouton "Ouvrir session" immédiatement
                            forceShowOpenButton = true
                            forceShowCloseButton = false

                            // Clôturer la session
                            mainViewModel.closeSessionCaisse(
                                baseConfig = mainViewModel.selectedBaseconfig,
                                sessionCaisse = sessionCaisse
                            )

                            // Forcer l'actualisation de l'interface
                            sessionUpdateTrigger += 1

                            // Forcer la mise à jour de la session
                            navDrawerViewModel.refreshSessionCaisse()

                            // Recharger les données du client pour mettre à jour l'interface
                            clientViewModel.getVisitesListByClient(client.cLICode)

                            // Lancer la synchronisation automatique
                            scope.launch {
                                    try {
                                        // Attendre que la clôture soit terminée
                                        delay(1000)

                                        // Lancer la synchronisation complète
                                        Log.d("ClientInfoPane", "Début de la synchronisation après clôture de session")
                                        mainViewModel.getSessionCaisseByUser(
                                            baseConfig = mainViewModel.selectedBaseconfig,
                                            codeUt = utilisateur.codeUt
                                        )

                                        // Attendre que la synchronisation soit terminée
                                        delay(2000)

                                        // Mettre à jour l'interface après la synchronisation
                                        Log.d("ClientInfoPane", "Mise à jour de l'interface après synchronisation")
                                        navDrawerViewModel.refreshSessionCaisse()

                                        // Vérifier l'état de la session après la mise à jour
                                        delay(500)
                                        Log.d("ClientInfoPane", "Session après mise à jour: sCIdSCaisse=${navDrawerViewModel.sessionCaisse.sCIdSCaisse}, sCClotCaisse=${navDrawerViewModel.sessionCaisse.sCClotCaisse}")

                                        // Synchronisation terminée avec succès
                                        Log.d("ClientInfoPane", "Synchronisation terminée avec succès")
                                    } catch (e: Exception) {
                                        Log.e("ClientInfoPane", "Erreur lors de la synchronisation: ${e.message}")
                                        e.printStackTrace()
                                    } finally {
                                        isSyncing = false
                                    }
                                }
                        } catch (e: Exception) {
                            Log.e("ClientInfoPane", "Erreur lors de la clôture de session: ${e.message}")
                            e.printStackTrace()
                            isSyncing = false
                            // Réinitialiser les états en cas d'erreur
                            forceShowCloseButton = false
                            forceShowOpenButton = false
                        }
                    }

                    mainViewModel.setFondCaisseDialogueVisibility(false)
                },
                onValueChange = {
                    mainViewModel.setfondcaisse(it)
                },
                text = mainViewModel.fondcaisse
            )
        }



        Box(
            Modifier
                .fillMaxSize()
                .padding(padding)
                .nestedScroll(nestedScrollConnection)
        ) {


            ClientInfoBody (
                navigate = { navigate(it) },
                proCaisseViewModels = proCaisseViewModels,
                mainViewModel = mainViewModel,
                locationViewModule = locationViewModule,
                navDrawerViewModel = navDrawerViewModel,
                isConnected = isConnected,
                utilisateur = utilisateur,
                client = client,
                clientId = clientId,
                sessionCaisse = sessionCaisse,
                addSessionCaisseState = addSessionCaisseState,
                listVilleDn = mainViewModel.listVilleDn,
                listeReglementLibreByClient = listeReglementLibreByClient,
                proCaisseAuthorization = proCaisseAuthorization,
                visitesList = visitesList,
                invPatList = invPatList,
                bcList = bcList,
                blList = blList,
                prefixList = prefixList,
                bonRetourList = bonRetourList,
                setFondCaisseDialogueVisibility = { mainViewModel.setFondCaisseDialogueVisibility(it) },
                forceShowOpenButton = forceShowOpenButton,
                forceShowCloseButton = forceShowCloseButton,
                generateCodeMPayment = { utilisateur, prefix ->
                    paymentViewModel.generateCodeM(utilisateur = utilisateur, prefix = prefix)
                },
                generateCodeM = { utilisateur, prefix ->
                    mainViewModel.generateCodeM(utilisateur = utilisateur, prefix = prefix)
                }
            )

            ClientInfoHeader(
                client = client,
                toolbarHeightPx = toolbarHeightPx,
                toolbarOffsetHeightPx = toolbarOffsetHeightPx,
                d = d,
                progress = progress
            )
        }

    }



}



