package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import kotlinx.serialization.Serializable

@Serializable
data class OrdreMissionWithLines(
    @Embedded
    var ordreMission: OrdreMission = OrdreMission(),
    @Relation(
        parentColumn = "ORD_Code",
        entityColumn = "LIGOR_Code"
    ) var ligneOrdreMission: List<LigneOrdreMission>? = null
)
