package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class PrixFilterListState(
    val lists: List<PrixVCWithImages> = emptyList(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByTypeCommunication: String = ""
)