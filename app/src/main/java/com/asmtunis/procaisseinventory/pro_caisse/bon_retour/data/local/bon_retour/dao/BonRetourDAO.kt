package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BON_RETOUR_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import kotlinx.coroutines.flow.Flow


@Dao
interface BonRetourDAO {
    @Query("SELECT * FROM $BON_RETOUR_TABLE " +
            "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour " +
            "where BOR_Station =:station" +
            " order by CAST( BOR_date AS DATE ) asc, BOR_Numero desc ")
    fun getByStation(station : String): Flow<Map<BonRetour, List<LigneBonRetour>>>


    @get:Query("SELECT * FROM $BON_RETOUR_TABLE " +
            "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour " +
            "where $BON_RETOUR_TABLE.isSync=0 and  ($BON_RETOUR_TABLE.Status='INSERTED'  or $BON_RETOUR_TABLE.Status='UPDATED') order by BOR_Numero desc")
    val notSynced: Flow<Map<BonRetour, List<LigneBonRetour>>?>
    @Transaction
    @Query(
        "SELECT * FROM $BON_RETOUR_TABLE " +

                "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour" +
                " WHERE $BON_RETOUR_TABLE.BOR_codefrs= :codeClient "+
                "order by CAST( BOR_date AS DATE ) asc, BOR_Numero desc "

    )
    fun getByClient(codeClient: String): Flow<Map<BonRetour, List<LigneBonRetourWithArticle>>>

    @get:Query("SELECT * FROM $BON_RETOUR_TABLE order by CAST( BOR_date AS DATE ) asc,BOR_Numero desc  ")
    val allMutable: Flow<List<BonRetour>>



    @Query("SELECT * FROM $BON_RETOUR_TABLE WHERE  BOR_codefrs=:codeClient and BOR_Exercice=:exercice order by BOR_date desc")
    fun getClientSolde(codeClient: String, exercice: String): Flow<List<BonRetour>>

    @Query("UPDATE $BON_RETOUR_TABLE SET BOR_codefrs = :code_client where BOR_codefrs = :oldCodeClient")
    fun updateCodeClient(code_client: String, oldCodeClient: String)

    @Query("UPDATE $BON_RETOUR_TABLE SET isSync = 1, Status= 'SELECTED', BOR_Numero = :bonRetourNum where BOR_Numero = :bonRetourNumM")
    fun setSynced(bonRetourNum : String, bonRetourNumM: String)

    @Query("SELECT ifnull(MAX(cast(substr(BOR_Numero,length(:prefix) + 1 ,length('BOR_Numero'))as integer)),0)+1 FROM $BON_RETOUR_TABLE WHERE substr(BOR_Numero, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BonRetour)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BonRetour>)

    @get:Query("SELECT count(*) FROM $BON_RETOUR_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Flow<Int>

    @get:Query("SELECT count(*) FROM $BON_RETOUR_TABLE")
    val count: Flow<Int>

    @get:Query("SELECT count(*) FROM $BON_RETOUR_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMutable: Flow<Int>

    @Query("DELETE FROM $BON_RETOUR_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $BON_RETOUR_TABLE where BOR_Numero= :codeRetour and BOR_Exercice=:exercice")
    fun deleteById(codeRetour: String, exercice: String)

    @Query("SELECT * FROM $BON_RETOUR_TABLE where BOR_Station=:station order by strftime('%Y-%m-%d %H-%M', BOR_date) DESC")
    fun getByStationMutable(station: String): Flow<List<BonRetour>>

    @get:Query("SELECT count(*) FROM $BON_RETOUR_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountNonMutable: Flow<Int>

    @Query("SELECT count(*) FROM $BON_RETOUR_TABLE where BOR_Station=:station")
    fun getAllCountBySession(station: String): Flow<Int>

    @Query("SELECT count(*) FROM $BON_RETOUR_TABLE where BOR_Station=:station")
    fun getAllCountBySessionMutable(station: String): Flow<Int>










    @Transaction
    @Query(
        "SELECT * FROM $BON_RETOUR_TABLE " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour" +
                " WHERE $BON_RETOUR_TABLE.BOR_codefrs  LIKE '%' || :searchString || '%' and BOR_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 1 THEN BOR_Numero END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 2 THEN BOR_Numero END DESC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BOR_Mnt_TTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BOR_Mnt_TTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END DESC "
    )
    fun filterByCodeFournisseur(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_RETOUR_TABLE " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour" +
                " WHERE $BON_RETOUR_TABLE.BOR_nomfrs LIKE '%' || :searchString || '%' and BOR_Station =:station" +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 1 THEN BOR_Numero END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 2 THEN BOR_Numero END DESC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BOR_Mnt_TTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BOR_Mnt_TTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END DESC "
    )
    fun filterByNomFournisseur(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_RETOUR_TABLE " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour" +
                " WHERE $BON_RETOUR_TABLE.BOR_Numero LIKE '%' || :searchString || '%' and BOR_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 1 THEN BOR_Numero END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 2 THEN BOR_Numero END DESC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BOR_Mnt_TTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BOR_Mnt_TTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END DESC "
    )
    fun filterByNumero(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_RETOUR_TABLE " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE} ON $BON_RETOUR_TABLE.BOR_Numero = ${ProCaisseConstants.LIGNE_BON_RETOUR_TABLE}.NumBon_Retour " +
                "where BOR_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 1 THEN BOR_Numero END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Numero'  AND :isAsc = 2 THEN BOR_Numero END DESC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BOR_Mnt_TTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'BOR_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BOR_Mnt_TTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_RETOUR_TABLE.BOR_date) END DESC "
    )
    fun getAllFiltred(isAsc: Int,  sortBy: String, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>

}