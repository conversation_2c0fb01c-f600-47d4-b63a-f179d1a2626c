package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.SUPERFICIE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import kotlinx.coroutines.flow.Flow


@Dao
interface SuperficieDAO {
    @get:Query("SELECT * FROM $SUPERFICIE_TABLE")
    val all: Flow<List<SuperficieDn>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(DnSuperficies: List<SuperficieDn>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(DnSuperficie: SuperficieDn)


    @Query("SELECT * FROM $SUPERFICIE_TABLE WHERE CodeSuperf=:codeSuperf")
    fun getbyCode(codeSuperf: String): SuperficieDn?


    @Query("delete from $SUPERFICIE_TABLE")
    fun deleteAll()

}