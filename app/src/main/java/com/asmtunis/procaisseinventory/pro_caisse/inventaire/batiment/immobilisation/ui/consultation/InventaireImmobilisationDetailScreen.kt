package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.HomeWork
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ItemDetailData
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.RowView
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels

@Composable
fun InventaireImmobilisationDetailScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    cameraViewModel: CameraViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    settingViewModel: SettingViewModel
) {
    val invPatrimoineViewModel = proCaisseViewModels.invPatViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val fiterValue = selectPatrimoineVM.fiterValue
    val selectedInvPatrimoine = invPatrimoineViewModel.selectedInvPatrimoine
    val listLigneBonCommande = invPatrimoineViewModel.selectedListLgInvPatrimoine
    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val marqueList = mainViewModel.marqueList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val imageList = mainViewModel.imageList

    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine

    val haveCamera = dataViewModel.getHaveCameraDevice()
   // val listImgeUri = listLigneBonCommande.mapNotNull { it.imageList }.flatten()
   // val listImgeUri: ArrayList<ImagePieceJoint> = arrayListOf()


    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!

    val openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog
    LaunchedEffect(key1 = Unit) {
     //   listImgeUri.clear()
        selectPatrimoineVM.resetSelectedPatrimoineArticles()
        for (i in listLigneBonCommande.indices) {
            val article = articleMapByBarCode[listLigneBonCommande[i].lGDEVCodeArt]?: Article(aRTCodeBar = listLigneBonCommande[i].lGDEVCodeArt)
            val imgList = imageList.filter { it.vcNumSerie ==  listLigneBonCommande[i].lGDevNumSerie }
        //    listImgeUri.addAll(imgList)

            selectPatrimoineVM.setConsultationSelectedPatrimoineList(
                article = article,
                numSerie = listLigneBonCommande[i].lGDevNumSerie ?: "N/A",
                quantity = StringUtils.stringToDouble(listLigneBonCommande[i].lGDEVQte),
                imageList = imgList,
                note = listLigneBonCommande[i].lgDEVNote ?: "",
                marque = marqueList.firstOrNull { it.mARCode == listLigneBonCommande[i].lGDEVCMarq } ?: Marque(mARDesignation = listLigneBonCommande[i].lGDEVCMarq?:"N/A")

            )
        }
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.invpat_number_field, selectedInvPatrimoine.dEVNum),
            )
        },
    ) { padding ->



        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact,
            WindowWidthSizeClass.Medium -> {

                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    canModify = false,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.batiment),
                        dataText = selectedInvPatrimoine.dEVClientName ?: "N/A",
                        icon = Icons.Default.HomeWork,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.date_field_title),
                        dataText = selectedInvPatrimoine.dEVDDmFormatted?: "N/A",
                        icon = Icons.Default.DateRange,
                        tint = LocalContentColor.current
                    ),
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                  //  imageList = listImgeUri,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {},
                    onDeleteClick = {},
                    onPressTakeImage = {},
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                         cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },
                    extraContent = {
                        if(selectedInvPatrimoine.dEVEtatBon == "1" && selectedInvPatrimoine.dEV_info3 == TypePatrimoine.SORTIE.typePat) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = stringResource(id = R.string.en_instance),
                                color = MaterialTheme.colorScheme.error,
                                style = MaterialTheme.typography.titleMedium,
                                // modifier = Modifier.align(Alignment.Start)
                            )
                        }
                    }


                )
            }
            WindowWidthSizeClass.Expanded -> {
                RowView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    padding = padding,
                    canModify = false,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.batiment),
                        dataText = selectedInvPatrimoine.dEVClientName ?: "N/A",
                        icon = Icons.Default.HomeWork,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.date_field_title),
                        dataText = selectedInvPatrimoine.dEVDDmFormatted?: "N/A",
                        icon = Icons.Default.DateRange,
                        tint = LocalContentColor.current
                    ),
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {},
                    onDeleteClick = {},
                    onPressTakeImage = {},
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },
                    extraContent = {
                        if(selectedInvPatrimoine.dEVEtatBon == "1" && selectedInvPatrimoine.dEV_info3 == TypePatrimoine.SORTIE.typePat) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = stringResource(id = R.string.en_instance),
                                color = MaterialTheme.colorScheme.error,
                                style = MaterialTheme.typography.titleMedium,
                                // modifier = Modifier.align(Alignment.Start)
                            )
                        }
                    }


                )
            }
            else -> {
                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    canModify = false,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.batiment),
                        dataText = selectedInvPatrimoine.dEVClientName?: "N/A",
                        icon = Icons.Default.HomeWork,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.date_field_title),
                        dataText = selectedInvPatrimoine.dEVDDm?: "N/A",
                        icon = Icons.Default.DateRange,
                        tint = LocalContentColor.current
                    ),
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {},
                    onDeleteClick = {},
                    onPressTakeImage = {},
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },
                    extraContent = {
                        if(selectedInvPatrimoine.dEVEtatBon == "1" && selectedInvPatrimoine.dEV_info3 == TypePatrimoine.SORTIE.typePat) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = stringResource(id = R.string.en_instance),
                                color = MaterialTheme.colorScheme.error,
                                style = MaterialTheme.typography.titleMedium,
                                // modifier = Modifier.align(Alignment.Start)
                            )
                        }
                    }


                )
            }
        }
    }




}


