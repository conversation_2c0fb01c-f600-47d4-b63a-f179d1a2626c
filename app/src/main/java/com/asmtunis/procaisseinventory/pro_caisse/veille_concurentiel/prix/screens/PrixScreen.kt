package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.navigation.PrixDetailRoute
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.PrixViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel


@Composable
fun PrixScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    listState: LazyListState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    cameraViewModel: CameraViewModel,
    vcViewModel: VeilleConcurentielViewModel,
    prixViewModel: PrixViewModel,
    mainViewModel: MainViewModel,
    textValidationViewModel: VcTextValidationViewModel = hiltViewModel()
){



   /* LaunchedEffect(key1 = prixViewModel.prixSearchTextState.text, key2 = prixState.lists, key3 = prixState.search) {
        prixViewModel.filterPrixVC(prixState)
    }*/


    val prixList = prixViewModel.prixVCListstate.lists





  if(prixList.isNotEmpty()) {
      ListPrixVc(
          navigate = { navigate(it) },
          popBackStack = { popBackStack() },
          isConnected = isConnected,
          selectedBaseconfig = selectedBaseconfig,
          getProCaisseDataViewModel = getProCaisseDataViewModel,
          listState = listState,
          cameraViewModel = cameraViewModel,
          textValidationViewModel = textValidationViewModel,
          prixList = prixList,
          vcViewModel = vcViewModel,
          prixViewModel = prixViewModel,
          utilisateur = mainViewModel.utilisateur,
          mainViewModel = mainViewModel
      )
  }
    else Column(
      modifier = Modifier.fillMaxSize(),
      verticalArrangement = Arrangement.Center,
      horizontalAlignment = Alignment.CenterHorizontally
  ) {
      LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
  }
}

@Composable
fun ListPrixVc (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    listState: LazyListState,
    cameraViewModel: CameraViewModel,
    textValidationViewModel: VcTextValidationViewModel,
    prixList: List<PrixVCWithImages>,
    vcViewModel: VeilleConcurentielViewModel,
    prixViewModel: PrixViewModel,
    mainViewModel: MainViewModel,
    utilisateur: Utilisateur
){

    val context = LocalContext.current
    val isRefreshing  = getProCaisseDataViewModel.prixState.loading
    val prefixList  = mainViewModel.prefixList

    PullToRefreshLazyColumn(
        items = prixList,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !prixList.any { it.prixVC?.isSync != true } && isConnected,
        onRefresh = {
            getProCaisseDataViewModel.getVCPrix(baseConfig = selectedBaseconfig)
        },
        key = { priceList -> priceList.prixVC?.id?: priceList },
        content = { priceList ->
            ListItem(
                onItemClick = {

                    val prefixe = prefixList.firstOrNull { it.pREIdTable == "VCPrix" }?.pREPrefixe?: "VC_P_M_IMG"
                    val prefixImage = prefixList.firstOrNull { it.pREIdTable == "VC_Image" }?.pREPrefixe?: "VC_P_M"

                    mainViewModel.generateCodeM(utilisateur = utilisateur,
                        prefix = prefixe,
                        prefixImage = prefixImage
                    )

                    cameraViewModel.addListImageUri(priceList.imageList?: emptyList())
                    prixViewModel.onselectedPrixChange(priceList)

                    textValidationViewModel.resetVariable()
                    prixViewModel.onModifyChange(false)
                  //  cameraViewModel.addImageUri(imgUri = EMPTY_IMAGE_URI, context = context)
                    navigate(PrixDetailRoute)
                },
                firstText = priceList.prixVC?.codeVCPrix?: "N/A",
                secondText = stringResource(id = R.string.type_communication_value, vcViewModel.typeCommunicationVCList.firstOrNull { it.codeTypeCom == priceList.prixVC?.codeTypeCom }?.typeCommunication?: priceList.prixVC?.codeTypeCom?: ""),
                thirdText =  stringResource(id = R.string.art_concurrent, priceList.prixVC?.articleConcur?: ""),

                dateText = priceList.prixVC?.dateOp?: "N/A",
                isSync = priceList.prixVC?.isSync?: false,
                status = priceList.prixVC?.status?: "N/A",
                onResetDeletedClick = {
                    prixViewModel.restDeletedPrix(priceList)
                },
                onMoreClick = {
                    prixViewModel.onselectedPrixChange(priceList)
                    vcViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        }
    )
    
     
}