package com.asmtunis.procaisseinventory.pro_caisse.tournee.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.google.android.gms.maps.model.Marker

@Composable
fun CustomMarkerInfoWindow (
    marker: Marker,
    client: Client,
    clientOrder: Int = -1,
    noteLgOrdreMission: String = "") {

    val adresse = marker.snippet ?: client.cLIAdresse?: ""
    Box(
        modifier = Modifier
            .background(
                color = MaterialTheme.colorScheme.onPrimary,
                shape = RoundedCornerShape(35.dp, 35.dp, 35.dp, 35.dp)
            )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if(clientOrder != -1) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.ordre, (clientOrder + 1).toString()),
                    textAlign = TextAlign.Center
                )
            }
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = marker.title?: client.cLINomPren,
                textAlign = TextAlign.Center
            )

            if(adresse.isNotEmpty()) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.adresse_field_title_extra, adresse),
                    textAlign = TextAlign.Center
                )
            } 
            if(noteLgOrdreMission.isNotEmpty()) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.note_field_extra, noteLgOrdreMission),
                    textAlign = TextAlign.Center
                )
            }

            /* OutlinedButton(onClick = {
                 onInfoWindowLongClick(clt)
             }) {

                 Icon(
                     imageVector = Icons.Outlined.Add,
                     contentDescription = stringResource(id = R.string.your_divice_id)
                 )

             }*/
        }

    }
}