package com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.ArrowDropDown
import androidx.compose.material.icons.twotone.ArrowDropUp
import androidx.compose.material.icons.twotone.ChevronRight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.navigation.StatGraphRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.Stat
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.components.BaseRow
import com.dokar.sonner.ToasterState

@Composable
fun VendorDashBoard(
    navigateTo: (route: Any) -> Unit,
    dashboardScreenVM: DashboardScreenViewModel,
    toaster: ToasterState,
    haveBLAuthorisation: Boolean,
    montantTotalStck: Double
) {
    val reglementPayments = dashboardScreenVM.reglementPayments

     val rallyDefaultPadding = 12.dp
   val nbrBl = reglementPayments.nbrBl
   val ca = reglementPayments.ca

    val nbrBL =
        if (stringToDouble(nbrBl) == 0.0) {
            1.0
        } else {
           stringToDouble(nbrBl)
        }


    val mntCheque = reglementPayments.mntCheque
    val mntEspece = reglementPayments.mntEspece
    val mntTraite = reglementPayments.mntTraite
    val regTotalMnt = mntCheque + mntEspece + mntTraite

    val density = LocalDensity.current

    val listNTopClients = reglementPayments.listNTopClients
    val showDetailVente = dashboardScreenVM.showDetailVente

    Spacer(Modifier.height(9.dp))
    OutlinedCard {
        BaseRow(
            modifier = Modifier.padding(rallyDefaultPadding).clickable {},
            title = stringResource(id = R.string.amount_stock),
            showArrow = false,
            imageVector = Icons.TwoTone.ChevronRight,
            amount = montantTotalStck,
            color = MaterialTheme.colorScheme.onTertiary,
        )
    }

    Spacer(Modifier.height(9.dp))
    if (haveBLAuthorisation) {
        OutlinedCard {
            BaseRow(
                modifier = Modifier
                    .padding(rallyDefaultPadding)
                    .animateContentSize()
                    .clickable { dashboardScreenVM.onShowDetailVenteChange(!showDetailVente) },
                title = stringResource(id = R.string.pannier_moyen),
                showArrow = true,
                imageVector = if (showDetailVente) Icons.TwoTone.ArrowDropUp else Icons.TwoTone.ArrowDropDown,
                amount = ca / nbrBL,
                color = MaterialTheme.colorScheme.outline,
            )
            val vente: List<Stat> =
                listOf(
                    Stat(
                        name = stringResource(id = R.string.chiffre_affaire),
                        balance = ca,
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                    ),
                    Stat(
                        name = stringResource(id = R.string.number_bon_livraison),
                        balance = stringToDouble(nbrBl),
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.6f),
                    ),
                )

            AnimatedVisibility(
                visible = showDetailVente,
                enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 }),
            ) {
                Column {
                    BaseRow(
                        modifier = Modifier.padding(rallyDefaultPadding),
                        title = vente.first().name,
                        showArrow = false,
                        imageVector = Icons.TwoTone.ArrowDropUp,
                        amount = vente.first().balance?: 0.0,
                        color = vente.first().color,
                    )
                    Spacer(Modifier.height(9.dp))
                    BaseRow(
                        modifier = Modifier.padding(rallyDefaultPadding),
                        title = vente.last().name,
                        showArrow = false,
                        formatAmount = false,
                        imageVector = Icons.TwoTone.ArrowDropUp,
                        amount = vente.last().balance?: 0.0,
                        color = vente.last().color,
                    )
                }
            }
        }

        Spacer(Modifier.height(9.dp))

        OutlinedCard {
            BaseRow(
                modifier = Modifier
                    .padding(rallyDefaultPadding)
                    .clickable { if(regTotalMnt != 0.0) navigateTo(StatGraphRoute) },
                title = stringResource(id = R.string.reglement_total),
                showArrow = regTotalMnt != 0.0,
                imageVector = Icons.TwoTone.ChevronRight,
                amount = regTotalMnt,
                color = MaterialTheme.colorScheme.inverseSurface,
            )
        }
    }


        Spacer(modifier = Modifier.height(30.dp))


        AnimatedVisibility(
            visible = listNTopClients.isNotEmpty(),
            enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
            exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
        ) {
            ClientBarChart(
                listNTopClients = listNTopClients,
                toaster = toaster
            )
        }

        Spacer(modifier = Modifier.height(90.dp))

}