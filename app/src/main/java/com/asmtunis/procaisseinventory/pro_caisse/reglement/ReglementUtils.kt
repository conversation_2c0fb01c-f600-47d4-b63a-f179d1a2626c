package com.asmtunis.procaisseinventory.pro_caisse.reglement

import android.content.Context
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.enum_classes.TicketState
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse

object ReglementUtils {
    private fun getTicket(reglementCaisse: ReglementCaisse, listTicket: List<Ticket>): Ticket? =
       // listTicket.firstOrNull { it.tIKExerc == reglementCaisse.rEGCExercice && it.tIKNumTicket == reglementCaisse.rEGCNumTicket.toString() }
        listTicket.firstOrNull { it.tIKExerc == reglementCaisse.rEGCExercice && it.tIKNumTicketM == reglementCaisse.rEGCCode_M.toString() }


    fun getReglementType(
        reglementCaisse: ReglementCaisse,
        context: Context,
        listTicket: List<Ticket>
    ): String {

        val ticket =
            if (reglementCaisse == ReglementCaisse()) listTicket.firstOrNull() else getTicket(
                reglementCaisse,
                listTicket
            )

        return when {
            ticket?.tIKEtat == TicketState.PAYED.getValue() -> context.getString(R.string.payment_label)
            else -> determineReglementTypeFromCaisse(reglementCaisse, context)
        }
    }

    private fun determineReglementTypeFromCaisse(
        reglementCaisse: ReglementCaisse,
        context: Context
    ): String {

        val regNumTicketPart = reglementCaisse.rEGNumTicketPart?.let {
            try {
                StringUtils.stringToDouble(it)
            } catch (e: NumberFormatException) {
                null // Handle the case where the string is not a valid number
            }
        }
        val regCNumTicket = reglementCaisse.rEGCNumTicket?.let {
            try {
                StringUtils.stringToDouble(it)
            } catch (e: NumberFormatException) {
                null // Handle the case where the string is not a valid number
            }
        }


        return when {
            regNumTicketPart != null && regNumTicketPart > 0 -> context.getString(R.string.paiement_Partiel)
            reglementCaisse.rEGCRemarque == Globals.REGLER_ACPT -> context.getString(R.string.reglementLibre)
            regCNumTicket != null && regCNumTicket > 0 -> context.getString(R.string.payment_label)
            else -> context.getString(R.string.credit_label)
        }
    }





    private const val BON_LIVRAISON_ID = "Bon_livraison"
    private const val DEFAULT_BL_PREFIX = "BL_M_"
    private const val TICKET_CANCELLED_SUFFIX = " (Annuler)"
    private const val PARTIAL_PAYMENT_SUFFIX = " (Paiement Partiel)"
    private const val CREDIT_TEXT = "Credit"

    fun getReglementNumber(
        reglementCaisse: ReglementCaisse,
        context: Context,
        ticket: Ticket?,
        prefixList: List<Prefixe>
    ): String {
        val prefixeBl = getBonLivraisonPrefix(prefixList)
        return when {
            ticket != null -> getTicketNumber(reglementCaisse, ticket, context, prefixeBl)
            else -> getReglementCode(reglementCaisse)
        }
    }

    private fun getBonLivraisonPrefix(prefixList: List<Prefixe>): String {
        return prefixList.firstOrNull { it.pREIdTable == BON_LIVRAISON_ID }?.pREPrefixe ?: DEFAULT_BL_PREFIX
    }

    private fun getTicketNumber(
        reglementCaisse: ReglementCaisse,
        ticket: Ticket,
        context: Context,
        prefixeBl: String
    ): String {
        var numTicket = when {
            ticket.tIKNumeroBL.isNullOrEmpty() -> prefixeBl + ticket.tIKNumTicket
            else -> context.getString(R.string.fact, ticket.tIKNumeroBL)
        }

        if (ticket.tIKAnnuler == "1") {
            numTicket += TICKET_CANCELLED_SUFFIX
        }

        if (isPartialPayment(reglementCaisse)) {
            numTicket += PARTIAL_PAYMENT_SUFFIX
        } else if (reglementCaisse.rEGCNumTicket == "0") {
            numTicket = CREDIT_TEXT
        }

        return numTicket
    }

    private fun getReglementCode(reglementCaisse: ReglementCaisse): String {
        return if (isPartialPayment(reglementCaisse) || (reglementCaisse.rEGCNumTicket?.toIntOrNull() ?: 0) > 0) {
            PARTIAL_PAYMENT_SUFFIX
        } else {
            reglementCaisse.rEGCCode.toString()
        }
    }

    private fun isPartialPayment(reglementCaisse: ReglementCaisse): Boolean {
        return (reglementCaisse.rEGNumTicketPart?.toLongOrNull() ?: 0L) != 0L
    }


}