package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.InventaireBatimentApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.InventaireBatimentApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object InventaireBatimentRemoteModule {

    @Provides
    @Singleton
    fun provideInventaireBatimentApi(client: HttpClient): InventaireBatimentApi = InventaireBatimentApiImpl(client)

}