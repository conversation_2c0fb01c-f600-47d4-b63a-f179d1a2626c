/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.components

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import java.text.DecimalFormat
import kotlin.math.abs


/**
 * A row representing the basic information of a Bill.
 */


@SuppressLint("SuspiciousIndentation")
@Composable
 fun BaseRow(
    modifier: Modifier = Modifier,
    color: Color,
    title: String,
    showArrow: Boolean,
    formatAmount: Boolean = true,
    imageVector: ImageVector,
   // subtitle: String,
    amount: Double
) {
 val formattedAmount = if(formatAmount)
     convertStringToPriceFormat(amount.toString())
 else removeTrailingZeroInDouble(convertDoubleToDoubleFormat(amount))
   // val formattedAmount = amount
    Row(
        modifier = modifier
            .height(68.dp)
            .clearAndSetSemantics {
                contentDescription =
                    " current balance $formattedAmount"
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        AccountIndicator(
            color = color,
            modifier = Modifier
        )

        Spacer(Modifier.width(12.dp))


        Column(
            modifier = Modifier.align(Alignment.CenterVertically),
            verticalArrangement = Arrangement.Center,
              horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = title,
                fontSize = MaterialTheme.typography.titleMedium.fontSize,
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight
            )

            Text(
                text = formattedAmount,
                //  style = typography.h6,

            )
        }


        Spacer(Modifier.width(16.dp))
        Spacer(Modifier.weight(1f))
          if(showArrow)  Icon(
                imageVector = imageVector,
                contentDescription = null,
                modifier = Modifier
                    .padding(end = 12.dp)
                    .size(24.dp)
            )

    }
 }

/**
 * A vertical colored line that is used in a [BaseRow] to differentiate accounts.
 */
@Composable
private fun AccountIndicator(color: Color, modifier: Modifier = Modifier) {
    Spacer(
        modifier
            .size(4.dp, 36.dp)
            .background(color = color)
    )
}



fun formatAmount(amount: Float): String {
    return AmountDecimalFormat.format(amount)
}

private val AmountDecimalFormat = DecimalFormat("#,###.##")

/**
 * Used with accounts and bills to create the animated circle.
 */
fun <E> List<E>.extractProportions(selector: (E) -> Float): List<Float> {
    val total = this.sumOf { abs(selector(it)).toDouble() }
    return this.map { abs((selector(it) / total).toFloat()) }
}
