package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.VerticalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.simapps.ui_kit.custom_cards.ItemDetail
import java.util.Locale

@Composable
fun ColumnView(
    articleMapByBarCode: Map<String, Article>,
    marqueList: List<Marque>,
    haveCamera: Boolean,
    selectedPatrimoine: SelectedPatrimoine,
    firstItemDetail: ItemDetailData,
    canModify: Boolean = true,
    onClickFirstItemDetail: () -> Unit = {},
    secondItemDetail: ItemDetailData?,
    padding: PaddingValues,
    selectedPatrimoineList: List<SelectedPatrimoine>,
   // imageList: List<ImagePieceJoint>,
    fiterValue: String,
    showFilterLine: Boolean,
    openVerticalalImagePagerDialog: Boolean,
    onOpenVerticalalImagePagerDialogChange: (Boolean) -> Unit,
    onPress: (SelectedPatrimoine) -> Unit,
    onShowFilterLineChange: (Boolean) -> Unit,
    onFilterValueChange: (String) -> Unit,
    onLongPress: (Pair<Boolean, SelectedPatrimoine>) -> Unit,
    onDeleteClick: (ImagePieceJoint) -> Unit,
    onPressTakeImage: (SelectedPatrimoine) -> Unit,
    onPressSeeImage: (SelectedPatrimoine) -> Unit,
    extraContent: @Composable () -> Unit = {}


) {
    Column(
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .padding(padding)
    ) {


        ItemDetail(
            modifier = firstItemDetail.modifier,
            //  title = stringResource(id = R.string.societe),
            title = firstItemDetail.title,
            dataText = firstItemDetail.dataText,
            icon = firstItemDetail.icon,
            onClick = { onClickFirstItemDetail() }
        )
        if(secondItemDetail != null) {
            Spacer(modifier = Modifier.height(12.dp))
            ItemDetail(
                //  title = stringResource(id = R.string.societe),
                title = secondItemDetail.title,
                dataText = secondItemDetail.dataText,
                icon = secondItemDetail.icon,
                tint = secondItemDetail.tint
            )
        }

        extraContent()

        Spacer(modifier = Modifier.height(12.dp))


        ThreeColumnTableWithImage(
            articleMapByBarCode = articleMapByBarCode,
            haveCamera = haveCamera,
            marqueList = marqueList,
            showFilterLine = selectedPatrimoineList.size > 6 && showFilterLine,
            onShowFilterLineChange = { onShowFilterLineChange(it) },
            fiterValue = fiterValue,
            onFilterValueChange = { onFilterValueChange(it) },
            canModify = canModify,
            selectedPatrimoineList = if(fiterValue.isNotEmpty()) selectedPatrimoineList.filter { it.numSerie.lowercase(
                Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) || it.articleCode.lowercase(
                Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT))  || articleMapByBarCode[it.articleCode]?.aRTDesignation?.lowercase(
                Locale.ROOT)?.contains(fiterValue.lowercase(Locale.ROOT)) == true
            } else selectedPatrimoineList,
            onPress = { onPress(it) },
            onLongPress = { onLongPress(Pair(true, it)) },
            onSwipeToDelete = {
                // invPatViewModel.onShowAlertDialogChange(Pair(true, it))
                //  selectPatrimoineVM.deleteItemToSelectedPatrimoineList(it)
            },
            onPressTakeImage = { onPressTakeImage(it) },
            onPressSeeImage = { onPressSeeImage(it) },
        )


        /*   HorizontalImagePager(
               onClicks = {

                   //vcViewModel.onOpenVerticalalImagePagerDialogChange(true)
               },
               canModify = true,
               cameraViewModel = cameraViewModel,

               imageList =  cameraViewModel.listImgeUri,
               onDeleteClick = {
                //   vcViewModel.onImageDeleted(it)
                   if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
               }
           )*/

      //  if (openVerticalalImagePagerDialog && selectedPatrimoineList.map { it.imageList }.isNotEmpty()) {
        if (openVerticalalImagePagerDialog && selectedPatrimoine.imageList.isNotEmpty()) {
            VerticalImagePager(
                canModify = true,
                onDismissRequest = { onOpenVerticalalImagePagerDialogChange(false) },
                //  imageList =  cameraViewModel.listImgeUri.filter { it.devNum == selectedPatrimoine.numSerie },
                //   imageList =  selectedPatrimoineList.flatMap { it.imageList.toList() }.toList().filter { it.devNum == selectedPatrimoine.numSerie },
                imageList =  selectedPatrimoine.imageList,

                onDeleteClick = {
                    //    cameraViewModel.onImageDeleted(it)
                    onDeleteClick(it)
                }
            )
        }

    }
}

