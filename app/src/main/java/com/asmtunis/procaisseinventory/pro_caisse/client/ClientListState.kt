package com.asmtunis.procaisseinventory.pro_caisse.client


import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch

data class ClientListState(
    val lists: List<Client> = emptyList(),
    val listOrder: ListOrder = ListOrder.Title(OrderType.Ascending),
    val filter: ListSearch = ListSearch.FirstSearch(),
    val filterByType: String = "",
    val filterByClientEtat: String = "",

    )
