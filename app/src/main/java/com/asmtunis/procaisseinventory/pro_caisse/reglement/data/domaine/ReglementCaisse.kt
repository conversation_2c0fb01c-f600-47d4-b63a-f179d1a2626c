package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.REGLEMENT_CAISSE_TABLE,primaryKeys = ["REGC_Code", "REGC_Exercice", "REGC_IdSCaisse", "REGC_Code_M"])
@Entity(tableName = ProCaisseConstants.REGLEMENT_CAISSE_TABLE)
@Serializable
data class ReglementCaisse  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @ColumnInfo(name = "REGC_Code")
    @SerialName("REGC_Code")
    val rEGCCode: String = "",



    @ColumnInfo(name = "REGC_Code_M")
    @SerialName("REGC_Code_M")
    val rEGCCode_M: String = "",

    @ColumnInfo(name = "REGC_Exercice")
    @SerialName("REGC_Exercice")
    val rEGCExercice: String = "",

    @ColumnInfo(name = "REGC_IdCarnet")
    @SerialName("REGC_IdCarnet")
    val rEGCIdCarnet: String? = "",

    @ColumnInfo(name = "REGC_NumTicket")
    @SerialName("REGC_NumTicket")
    val rEGCNumTicket: String? = "",

    @ColumnInfo(name = "REGC_IdSCaisse")
    @SerialName("REGC_IdSCaisse")
    val rEGCIdSCaisse: String = "",


    @ColumnInfo(name = "REGC_NumTicketPart")
    @SerialName("REGC_NumTicketPart")
    val rEGNumTicketPart: String? = null,

    @ColumnInfo(name = "REGC_IdCaisse")
    @SerialName("REGC_IdCaisse")
    val rEGCIdCaisse: String? = "",

    @ColumnInfo(name = "REGC_IdStation")
    @SerialName("REGC_IdStation")
    val rEGCIdStation: String? = "",

    @ColumnInfo(name = "REGC_CodeClient")
    @SerialName("REGC_CodeClient")
    val rEGCCodeClient: String? = "",

    @ColumnInfo(name = "REGC_NomPrenom")
    @SerialName("REGC_NomPrenom")
    val rEGCNomPrenom: String? = "",

    @SerialName("REGC_ModeReg")
    @ColumnInfo(name = "REGC_ModeReg")
    val rEGCModeReg: String? = "",

    @SerialName("REGC_DateReg")
    @ColumnInfo(name = "REGC_DateReg")
    val rEGCDateReg: String? = "",

    @SerialName("REGC_MntEspece")
    @ColumnInfo(name = "REGC_MntEspece")
    val rEGCMntEspece: Double? = null,

    @SerialName("REGC_MntCarteBancaire")
    @ColumnInfo(name = "REGC_MntCarteBancaire")
    val rEGCMntCarteBancaire: Double? = null,

    @SerialName("REGC_MntCh\u00e9que")
    @ColumnInfo(name = "REGC_MntCheque")
    val rEGCMntCheque: Double? = null,

    @SerialName("REGC_MntTraite")
    @ColumnInfo(name = "REGC_MntTraite")
    val rEGCMntTraite: Double? =null,

    @SerialName("REGC_Remarque")
    @ColumnInfo(name = "REGC_Remarque")
    val rEGCRemarque: String? = "",

    @SerialName("REGC_Montant")
    @ColumnInfo(name = "REGC_Montant")
    val rEGCMontant: Double? = null,

    @SerialName("REGC_Station")
    @ColumnInfo(name = "REGC_Station")
    val rEGCStation: String? = "",

    @SerialName("REGC_User")
    @ColumnInfo(name = "REGC_User")
    val rEGCUser: String? = "",

    @SerialName("REGC_MntTotalRecue")
    @ColumnInfo(name = "REGC_MntTotalRecue")
    val rEGCMntTotalRecue: Double? = null,

    @SerialName("REGC_MntEspeceRecue")
    @ColumnInfo(name = "REGC_MntEspeceRecue")
    val rEGCMntEspeceRecue: Double? = null,

    @SerialName("rest")
    @ColumnInfo(name = "rest")
    val rest: Double? = null,

    @SerialName("made")
    @ColumnInfo(name = "made")
    val made: Double? = null,


    // this field used only to prevent sync reglement when create new bl
    @ColumnInfo(name = "canSync")
    @Transient
    val canSync: Boolean = true,
): BaseModel()





   
 

