package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.FAMILLE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import kotlinx.coroutines.flow.Flow

@Dao
interface FamilleDnDAO {
    @get:Query("SELECT * FROM $FAMILLE_TABLE")
    val all: Flow<List<FamilleDn>>

    // LiveData<List<DNFamille>> getAll();
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(dnFamille: List<FamilleDn>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(dnFamille: FamilleDn)

    @Query("SELECT * FROM $FAMILLE_TABLE WHERE CodeFamille = :code")
    fun getByCode(code: String): FamilleDn?

    @Query("SELECT * FROM $FAMILLE_TABLE WHERE DesgFamille = :code")
    fun getByname(code: String): FamilleDn?

    @Query("delete from $FAMILLE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $FAMILLE_TABLE where CodeFamille=:codeFamille")
    fun deleteById(codeFamille: String) // @Query("UPDATE DNTypeServices SET CodeVCPrix = :code_procaiss where CodeVCPrixM = :CodeMobile")
    // void updateCloudCode(String code_procaiss, String CodeMobile);
}
